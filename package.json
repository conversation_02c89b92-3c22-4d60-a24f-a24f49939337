{"name": "root", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "ui:add": "pnpm --filter ui ui:add", "gc": "pnpm --filter sdk build", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@saf/eslint-config": "workspace:*", "@saf/typescript-config": "workspace:*", "prettier": "^3.3.2", "turbo": "latest"}, "packageManager": "pnpm@9.4.0", "engines": {"node": ">=18"}}