import { components } from "@saf/sdk"

type MiniAppStatus = components["schemas"]["MiniAppStatus"]
type MiniAppVersionStatus = components["schemas"]["MiniAppVersionStatus"]
type MiniAppType = components["schemas"]["MiniAppType"]
type DataSourceType = components["schemas"]["DataSourceType"]
type InternalTablePermissionType = components["schemas"]["InternalTablePermissionType"]
type InternalTableFieldType = components["schemas"]["InternalTableFieldType"]
type UserStatus = components["schemas"]["UserStatus"]
type SystemRole = components["schemas"]["SystemRole"]
type TeamRole = components["schemas"]["TeamRole"]

export type Option<T = string> = {
  label: string
  value: T
}

export type Options<T = string> = Option<T>[]

export const miniAppStatusOption: Options<MiniAppStatus> = [
  { label: "Removed By Admin", value: "removed_by_admin" },
  { label: "Active", value: "active" },
]

export const miniAppTypeOptions: Options<MiniAppType> = [
  { label: "URL", value: "url" },
  { label: "Bundle", value: "bundle" },
  { label: "Builder", value: "builder" },
]

export const miniAppVersionStatusOptions: Options<MiniAppVersionStatus> = [
  { label: "Draft", value: "draft" },
  { label: "In Review", value: "in_review" },
  { label: "Approved", value: "approved" },
  { label: "Live", value: "live" },
  { label: "Rejected", value: "rejected" },
]

export const dataSourceTypeOptions: Options<DataSourceType> = [
  { label: "Internal Table", value: "internal_table" },
  { label: "External API", value: "external_api" },
]

export const dataSourcePermissionTypeOptions: Options<InternalTablePermissionType> = [
  { label: "Allow", value: "allow" },
  { label: "Deny", value: "deny" },
  { label: "Owner", value: "owner" },
]

export const dataSourceTableFieldTypeOptions: Options<InternalTableFieldType> = [
  { label: "String", value: "string" },
  { label: "Number", value: "number" },
  { label: "Boolean", value: "boolean" },
  { label: "DateTime", value: "datetime" },
  { label: "Image", value: "image" },
]

export const userStatusOptions: Options<UserStatus> = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
  { label: "Suspended", value: "suspended" },
  { label: "Deleted", value: "deleted" },
]

export const systemRoleOptions: Options<SystemRole> = [
  { label: "System Admin", value: "system_admin" },
  { label: "System Editor", value: "system_editor" },
]

export const teamRoleOptions: Options<TeamRole> = [
  { label: "Team Admin", value: "team_admin" },
  { label: "Team Developer", value: "team_developer" },
]
