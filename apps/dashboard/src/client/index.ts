import { paths } from "@saf/sdk"
import createFetchClient, { Middleware } from "openapi-fetch"
import createQueryClient from "openapi-react-query"
import { createQueueMiddleware } from "./middleware"
import { tokenService } from "./token-service"

export const createSafClient = ({ baseUrl }: { baseUrl: string }) => {
  const safFetch = createFetchClient<paths>({
    baseUrl,
  })

  const authMiddleware: Middleware = {
    async onRequest({ request }) {
      try {
        const accessToken = tokenService.getAccessToken()
        if (accessToken) {
          request.headers.set("Authorization", `Bearer ${accessToken}`)
        }
        return request
      } catch (_) {
        return undefined
      }
    },
  }

  // Add the queue middleware first to intercept requests when token refresh is in progress
  safFetch.use(createQueueMiddleware())
  safFetch.use(authMiddleware)
  // Add the refresh token middleware last to handle 401 responses
  // TODO: revise this buggy middleware
  // safFetch.use(createRefreshTokenMiddleware({ baseUrl }))

  const safQuery = createQueryClient(safFetch)

  return {
    safFetch,
    safQuery,
  }
}

export const API_URL = import.meta.env.VITE_API_URL || ""

export const { safFetch, safQuery } = createSafClient({
  baseUrl: `${API_URL}`,
})
