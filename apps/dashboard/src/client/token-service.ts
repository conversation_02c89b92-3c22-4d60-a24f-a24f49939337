import { decodeToken } from "react-jwt"

export const ACCESS_TOKEN_KEY = "access_token"
export const REFRESH_TOKEN_KEY = "refresh_token"

export const tokenService = {
  getAccessToken: (): string | null => {
    return localStorage.getItem(ACCESS_TOKEN_KEY)
  },
  getUserId: (): number | null => {
    const token = localStorage.getItem(ACCESS_TOKEN_KEY)
    if (!token) return null

    try {
      const payload: { sub?: string } | null = decodeToken(token)
      if (!payload?.sub) return null
      return parseInt(payload.sub)
    } catch {
      return null
    }
  },
  getRefreshToken: (): string | null => {
    return localStorage.getItem(REFRESH_TOKEN_KEY)
  },
  saveTokens: ({ accessToken, refreshToken }: { accessToken: string; refreshToken: string }): void => {
    localStorage.setItem(ACCESS_TOKEN_KEY, accessToken)
    localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
  },
  clearTokens: (): void => {
    localStorage.removeItem(ACCESS_TOKEN_KEY)
    localStorage.removeItem(REFRESH_TOKEN_KEY)
  },
  isTokenExpiringSoon: (bufferSeconds: number = 300): boolean => {
    const token = localStorage.getItem(ACCESS_TOKEN_KEY)
    if (!token) return true

    try {
      const payload = JSON.parse(atob(token.split(".")[1]))
      return payload.exp < Date.now() / 1000 + bufferSeconds
    } catch {
      return true
    }
  },
}
