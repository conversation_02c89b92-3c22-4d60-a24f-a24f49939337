import { components, paths } from "@saf/sdk"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "msw"
import { createOpenApiHttp } from "openapi-msw"
import { API_URL } from ".."

const openApiHttp = createOpenApiHttp<paths>({
  baseUrl: API_URL,
})

export const handlers: RequestHand<PERSON>[] = [
  // openApiHttp.post('/api/admin/auth/login', ({ response }) => {
  //   return response(200).json({
  //     accessToken:
  //       'eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.n3O_YLhB7hk81T1OpZUrJ7AVmNRJSTv7ztDQLOASTRQ',
  //     refreshToken: 'fbc69367c0034e258d14448df6fce80c',
  //     user: {
  //       id: 8,
  //       name: 'Test Account [Hsing]',
  //       email: '<EMAIL>',
  //       userStatus: 1,
  //       systemRoles: ['system_admin', 'system_editor'],
  //       teams: [
  //         {
  //           id: 1,
  //           name: '69 TEAM [Hsing]',
  //           roles: ['team_admin', 'team_developer'],
  //         },
  //       ],
  //     },
  //   });
  // }),
  // openApiHttp.get('/api/admin/auth/me', ({ response }) => {
  //   return response(200).json({
  //     id: 8,
  //     name: 'Test Account [Hsing]',
  //     email: '<EMAIL>',
  //     userStatus: 1,
  //     systemRoles: ['system_admin', 'system_editor'],
  //     teams: [
  //       {
  //         id: 1,
  //         name: '69 TEAM [Hsing]',
  //         roles: ['team_admin', 'team_developer'],
  //       },
  //     ],
  //   });
  // }),
  openApiHttp.get("/api/admin/teams/{teamId}/mini-apps", ({ response }) => {
    return response(200).json({
      items: [
        {
          id: 1,
          name: "Sample MiniApp",
          description: "This is a sample mini app for testing purposes.",
          customerServiceContactNumber: "123-456-7890",
          customerServiceContactEmail: "<EMAIL>",
          termsAndConditionsUrl: "https://example.com/terms",
          miniAppStatus: "active",
          categoryId: 101,
          teamId: 202,
          createdAt: "2023-01-01T00:00:00Z",
          createdBy: "admin",
          updatedAt: "2023-01-02T00:00:00Z",
          updatedBy: "admin",
        },
      ],
      page: 1,
      pageSize: 10,
      totalCount: 1,
      totalPages: 1,
    })
  }),
  openApiHttp.get("/api/admin/teams/{teamId}/mini-apps/{miniAppId}", ({ params, response }) => {
    const id = parseInt(params.miniAppId as string)
    return response(200).json({
      id: id,
      name: `MiniApp ${id}`,
      description: `This is mini app ${id} for testing purposes.`,
      customerServiceContactNumber: "123-456-7890",
      customerServiceContactEmail: "<EMAIL>",
      termsAndConditionsUrl: "https://example.com/terms",
      miniAppStatus: "active",
      categoryId: 101,
      teamId: 202,
      createdAt: "2023-01-01T00:00:00Z",
      createdBy: "admin",
      updatedAt: "2023-01-02T00:00:00Z",
      updatedBy: "admin",
    })
  }),
  openApiHttp.post("/api/admin/teams/{teamId}/mini-apps", async ({ request, response }) => {
    const body = await request.json()
    return response(201).json({
      id: Math.floor(Math.random() * 1000) + 10,
      ...body,
      miniAppStatus: "active",
      teamId: 202,
      createdAt: new Date().toISOString(),
      createdBy: "admin",
    })
  }),
  openApiHttp.patch("/api/admin/teams/{teamId}/mini-apps/{miniAppId}", async ({ params, request, response }) => {
    const id = parseInt(params.miniAppId as string)
    const body = await request.json()
    return response(200).json({
      id: id,
      miniAppStatus: "active",
      teamId: 202,
      updatedAt: new Date().toISOString(),
      updatedBy: "admin",
      createdAt: "",
      createdBy: "",
      name: `MiniApp ${id}`,
      description: `This is mini app ${id} for testing purposes.`,
      customerServiceContactNumber: "123-456-7890",
      customerServiceContactEmail: "<EMAIL>",
      ...body,
    })
  }),
  openApiHttp.delete("/api/admin/teams/{teamId}/mini-apps/{miniAppId}", ({ response }) => {
    return response.untyped(new Response())
  }),
  openApiHttp.get("/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions", ({ response }) => {
    return response(200).json({
      items: [
        {
          id: 1,
          miniAppType: "builder",
          version: "1.0.1",
          releaseNote: "This release include fix for last issue",
          miniAppVersionStatus: "draft",
          thumbnailUrl: "",
          createdAt: new Date().toISOString(),
          createdBy: "Bani",
        },
        {
          id: 2,
          version: "1.0.0",
          miniAppType: "url",
          releaseNote: "Initial release",
          miniAppVersionStatus: "approved",
          thumbnailUrl: "",
          createdAt: new Date().toISOString(),
          createdBy: "Foo",
        },
      ],
      totalCount: 1,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    })
  }),
  openApiHttp.get("/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}", ({ response }) => {
    return response(200).json({
      id: 8,
      miniAppType: "builder",
      version: "1.0.0",
      releaseNote: "Release note of the app",
      miniAppVersionStatus: "draft",
      thumbnailUrl: "",
      createdAt: "",
      createdBy: "",
      pages: [
        {
          id: 1,
          name: "Utama",
          hideInNavbar: false,
          widgets: [
            {
              id: 1,
              type: "title",
              name: "Permohonan Pinjaman",
              style: "Simple",
              data: {
                title: "Permohonan Pinjaman",
                subtitle: "Sila isi borang di bawah untuk memohon pinjaman.",
              },
              design: {
                imageFill: "fill",
              },
              actions: [],
            } as components["schemas"]["TitleWidget"],
            {
              id: 2,
              type: "text_entry",
              name: "Nama Penuh",
            },
            {
              id: 3,
              type: "text_entry",
              name: "NRIC / Pasport",
            },
            {
              id: 4,
              type: "text_entry",
              name: "Email",
            },
            {
              id: 5,
              type: "text_entry",
              name: "Telefon No.",
            },
            {
              id: 7,
              type: "button",
              name: "Submit",
              design: {
                primary: "right",
              },
              actions: [
                {
                  actionType: "submit_form",
                  title: "Hantar",
                },
              ],
            } as components["schemas"]["ButtonWidget"],
            {
              id: 8,
              type: "text",
              name: "Penafian",
              content:
                "Maklumat yang diberikan adalah untuk tujuan permohonan pinjaman sahaja. Kelulusan pinjaman tertakluk kepada terma dan syarat yang ditetapkan oleh pihak institusi kewangan. Pihak kami tidak bertanggungjawab atas sebarang keputusan yang dibuat oleh institusi kewangan berkaitan permohonan ini.",
              design: {
                textAlign: "left",
                style: "footnote",
              },
            } as components["schemas"]["TextWidget"],
          ],
        },
        {
          id: 2,
          name: "My Semakan",
          hideInNavbar: false,
          widgets: [
            {
              id: 4,
              type: "collection",
              name: "Semakan Saya",
            },
          ],
        },
        {
          id: 3,
          name: "Produk",
          hideInNavbar: false,
          widgets: [
            {
              id: 5,
              type: "collection",
              name: "Senarai Produk",
            },
          ],
        },
        {
          id: 4,
          name: "Demo",
          hideInNavbar: false,
          widgets: [
            {
              id: 6,
              type: "collection",
              name: "Collection",
            },
            {
              id: 7,
              type: "title",
              name: "Title",
            },
            {
              id: 8,
              type: "form_container",
              name: "Form Container",
            },
            {
              id: 9,
              type: "text",
              name: "Text",
            },
            {
              id: 12,
              type: "rich_text",
              name: "Rich Text",
            },
            {
              id: 13,
              type: "alert",
              name: "Alert",
            },
            {
              id: 14,
              type: "fields",
              name: "Fields",
            },
            {
              id: 15,
              type: "image",
              name: "Image",
            },
            {
              id: 16,
              type: "video",
              name: "Video",
            },
            {
              id: 17,
              type: "big_number",
              name: "Big Number",
            },
            {
              id: 18,
              type: "button",
              name: "Button",
            },
            {
              id: 19,
              type: "link",
              name: "Link",
            },
            {
              id: 20,
              type: "text_entry",
              name: "Text Entry",
            },
            {
              id: 21,
              type: "date_time",
              name: "Date Time",
            },
            {
              id: 22,
              type: "date",
              name: "Date",
            },
            {
              id: 23,
              type: "time",
              name: "Time",
            },
            {
              id: 24,
              type: "number_entry",
              name: "Number Entry",
            },
            {
              id: 25,
              type: "phone_entry",
              name: "Phone Entry",
            },
            {
              id: 26,
              type: "email_entry",
              name: "Email Entry",
            },
            {
              id: 27,
              type: "checkbox",
              name: "Checkbox",
            },
            {
              id: 28,
              type: "image_picker",
              name: "Image Picker",
            },
            {
              id: 29,
              type: "file_picker",
              name: "File Picker",
            },
            {
              id: 30,
              type: "choice",
              name: "Choice",
            },
          ],
        },
      ],
    })
  }),
]
