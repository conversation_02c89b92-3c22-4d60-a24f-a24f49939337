import { UnhandledRequestPrint } from "node_modules/msw/lib/core/utils/request/onUnhandledRequest"
import { API_URL } from ".."

// Tell `msw` to ignore unhandled requests to known hosts.
export const ignoreUnhandledRequestToKnownHosts = (request: Request, print: UnhandledRequestPrint) => {
  if (
    request.url.startsWith("https://cognito-idp.ap-southeast-2.amazonaws.com") ||
    request.url.startsWith(location.origin)
  ) {
    return
  }

  print.warning()
}

// Construct absolute API URL for `msw` usage
export const buildApiUrl = (path?: string) => {
  return new URL(path ?? "", API_URL).toString()
}
