import { paths } from "@saf/sdk"
import createFetchClient, { Middleware } from "openapi-fetch"
import { tokenService } from "./token-service"

export class QueuedInterceptor {
  private isLocked = false
  private requestQueue: Array<{
    resolve: (request: Request) => void
    reject: (error: any) => void
    request: Request
  }> = []

  lock(): void {
    this.isLocked = true
  }

  unlock(): void {
    this.isLocked = false
    this.processQueue()
  }

  clear(): void {
    this.requestQueue.forEach(({ reject }) => {
      reject(new Error("Request was cancelled"))
    })
    this.requestQueue = []
  }

  private processQueue(): void {
    const queue = [...this.requestQueue]
    this.requestQueue = []

    queue.forEach(({ resolve, request }) => {
      resolve(request)
    })
  }

  enqueue(request: Request): Promise<Request> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ resolve, reject, request })
    })
  }

  // Instead of creating a middleware, we export methods that can be used in middleware
  isRequestLocked(): boolean {
    return this.isLocked
  }
}

export const queuedInterceptor = new QueuedInterceptor()

export const createQueueMiddleware = (): Middleware => {
  return {
    async onRequest({ request }) {
      if (queuedInterceptor.isRequestLocked()) {
        return queuedInterceptor.enqueue(request)
      }
      return request
    },
  }
}

export const createRefreshTokenMiddleware = ({ baseUrl }: { baseUrl: string }): Middleware => {
  return {
    async onResponse({ response, request }) {
      if (response.status === 401) {
        // Only handle refresh if we're not already locked
        if (!queuedInterceptor.isRequestLocked()) {
          queuedInterceptor.lock()

          const fetchClient = createFetchClient<paths>({
            baseUrl,
          })

          try {
            const refreshToken = tokenService.getRefreshToken()
            const userId = tokenService.getUserId()?.toString()

            if (!refreshToken || !userId) {
              throw new Error("No refresh token or user ID found")
            }

            const response = await fetchClient.POST("/api/admin/auth/refresh-token", {
              body: {
                refreshToken,
                userId,
              },
            })

            tokenService.saveTokens({
              accessToken: response.data?.accessToken || "",
              refreshToken: response.data?.refreshToken || "",
            })

            // Update the current request with the new token
            const newRequest = request.clone()
            const accessToken = tokenService.getAccessToken()
            if (accessToken) {
              newRequest.headers.set("Authorization", `Bearer ${accessToken}`)
            }

            // Unlock and process queue
            queuedInterceptor.unlock()

            // Retry the current request
            return fetch(newRequest)
          } catch (error) {
            // Clear the queue on refresh failure
            queuedInterceptor.clear()
            throw error
          }
        }
      }

      if (!response.ok) {
        throw await response.json()
      }

      return undefined
    },
  }
}
