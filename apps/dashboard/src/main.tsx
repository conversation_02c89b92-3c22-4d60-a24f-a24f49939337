import "@saf/ui/main.css"
import React from "react"
import ReactDOM from "react-dom/client"
import { App } from "./App"

import "./main.css"

async function configureMocking() {
  if (process.env.NODE_ENV !== "development" || import.meta.env.VITE_ENABLE_MOCK !== "true") {
    return
  }

  const { worker } = await import("./client/mocks/browser")

  return worker.start({
    onUnhandledRequest: "bypass",
  })
}

configureMocking().then(() => {
  ReactDOM.createRoot(document.getElementById("root")!).render(
    // <React.StrictMode>
    <App />,
    // </React.StrictMode>,
  )
})
