import { Photo } from "@medusajs/icons"
import { clx } from "@saf/ui"

type ThumbnailProps = {
  src?: string | null
  alt?: string
  size?: "small" | "base"
}

export const Thumbnail = ({ src, alt, size = "base" }: ThumbnailProps) => {
  return (
    <div
      className={clx(
        "flex items-center justify-center overflow-hidden rounded border border-ui-border-base bg-ui-bg-component",
        {
          "h-8 w-6": size === "base",
          "h-5 w-4": size === "small",
        },
      )}
    >
      {src ? (
        <img src={src} alt={alt} className="h-full w-full object-cover object-center" />
      ) : (
        <Photo className="text-ui-fg-subtle" />
      )}
    </div>
  )
}
