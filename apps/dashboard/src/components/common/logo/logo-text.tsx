export const LogoText = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg width="181" height="56" viewBox="0 0 181 56" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_1_31)">
        <path
          d="M26.6653 0.655264L46.3012 11.4681C47.0797 11.8952 47.7316 12.5201 48.1913 13.2797C48.651 14.0393 48.9021 14.9068 48.9193 15.7945L49.3644 38.1925C49.3817 39.0795 49.1651 39.9553 48.7366 40.732C48.308 41.5087 47.6825 42.1589 46.923 42.6172L27.7453 54.2023C26.9876 54.6619 26.1221 54.9136 25.2361 54.932C24.3502 54.9504 23.475 54.7348 22.6989 54.3071L3.05644 43.4615C2.27757 43.035 1.62524 42.4102 1.16548 41.6504C0.705724 40.8907 0.454872 40.0229 0.438322 39.1351L-0.000213304 16.7371C-0.0168032 15.8503 0.200018 14.9747 0.628514 14.1981C1.05701 13.4215 1.68212 12.7712 2.44118 12.3124L21.5993 0.727262C22.3626 0.270348 23.2331 0.0230708 24.1226 0.0104286C25.0122 -0.00221371 25.8893 0.220227 26.6653 0.655264Z"
          fill="#2563EB"
        />
        <path d="M41.0389 17.5676L24.6756 28.3608L7.92621 18.2221L24.296 8.33215L41.0389 17.5676Z" fill="#F9A719" />
        <path d="M41.4187 36.6864L25.0489 46.5698L24.6758 28.3608L41.0391 17.5676L41.4187 36.6864Z" fill="#FAD206" />
        <path d="M25.0487 46.5698L7.97857 37.3409L7.92621 18.2221L24.6756 28.3608L25.0487 46.5698Z" fill="#F5871F" />
        <path d="M7.97815 37.3409L16.2907 32.1178V3.94681L7.97815 8.96705V37.3409Z" fill="#F5871F" />
        <path
          d="M24.4594 16.5662L33.9894 21.822L34.2054 32.7069L24.8849 38.3358L15.3549 33.08L15.1389 22.1951L24.4594 16.5662Z"
          fill="#2563EB"
        />
        <path d="M66.5146 14.6209V41.3925H62.5089V14.6209H66.5146Z" fill="#2563EB" />
        <path
          d="M72.2028 24.5084H76.1084V26.0706C76.7021 25.4423 77.417 24.9409 78.2099 24.5967C79.0028 24.2524 79.8573 24.0725 80.7216 24.0677C81.5771 24.0193 82.4328 24.1599 83.2278 24.4796C84.0228 24.7992 84.7376 25.29 85.3215 25.917C86.3007 26.9541 86.7903 28.6632 86.7903 31.0444V41.3925H82.8714V31.9657C82.8714 30.2966 82.6488 29.1461 82.2038 28.5141C81.7498 27.8465 80.9286 27.5394 79.7402 27.5394C79.2075 27.4884 78.6708 27.5811 78.1859 27.8076C77.701 28.0342 77.2857 28.3865 76.983 28.8279C76.3999 29.6691 76.1084 31.1423 76.1084 33.2475V41.3925H72.1895L72.2028 24.5084Z"
          fill="#2563EB"
        />
        <path
          d="M91.6841 24.5083H95.6031V26.0706C96.1961 25.4414 96.9108 24.9394 97.7039 24.595C98.4969 24.2507 99.3518 24.0713 100.216 24.0677C101.07 24.0155 101.924 24.1517 102.719 24.4667C103.514 24.7816 104.23 25.2677 104.816 25.8903C105.764 26.9452 106.238 28.6632 106.238 31.0443V41.3925H102.319V31.9657C102.319 30.2966 102.097 29.1461 101.652 28.5141C101.198 27.8464 100.377 27.5393 99.1882 27.5393C98.6555 27.4891 98.1191 27.5821 97.6343 27.8086C97.1496 28.0351 96.7341 28.387 96.4309 28.8278C95.8968 29.669 95.6365 31.1445 95.6365 33.2475V41.3925H91.7175L91.6841 24.5083Z"
          fill="#2563EB"
        />
        <path
          d="M110.357 32.8269C110.339 31.6645 110.563 30.511 111.015 29.4398C111.466 28.3685 112.136 27.403 112.981 26.6047C114.701 24.9531 116.993 24.0307 119.377 24.0307C121.761 24.0307 124.053 24.9531 125.773 26.6047C126.619 27.4265 127.287 28.4138 127.735 29.5049C128.183 30.5961 128.401 31.7678 128.376 32.9471C128.404 34.1319 128.186 35.3097 127.736 36.4059C127.285 37.5022 126.612 38.4931 125.759 39.3162C124.905 40.1509 123.893 40.8066 122.782 41.2445C121.671 41.6825 120.484 41.894 119.29 41.8665C118.107 41.895 116.931 41.6785 115.836 41.2303C114.741 40.7822 113.751 40.1123 112.928 39.2628C112.082 38.4224 111.418 37.4178 110.976 36.3109C110.534 35.2041 110.324 34.0183 110.357 32.8269ZM114.363 32.9003C114.278 34.3573 114.756 35.7912 115.698 36.9061C116.679 37.8667 117.997 38.4048 119.37 38.4048C120.743 38.4048 122.061 37.8667 123.042 36.9061C123.994 35.8213 124.488 34.4088 124.417 32.9671C124.488 31.5253 123.995 30.1125 123.042 29.0281C122.575 28.5322 122.006 28.1426 121.375 27.8857C120.744 27.6289 120.064 27.5107 119.384 27.5393C118.709 27.5112 118.036 27.6298 117.411 27.8868C116.786 28.1438 116.225 28.5332 115.765 29.0281C114.801 30.0817 114.292 31.4735 114.35 32.9003H114.363Z"
          fill="#2563EB"
        />
        <path
          d="M160.496 41.8865H155.415L153.319 39.7301C151.097 41.1624 148.505 41.9122 145.862 41.8865C142.369 41.9106 138.995 40.6214 136.408 38.2747C134.954 37.0231 133.797 35.4639 133.02 33.71C132.243 31.956 131.865 30.0512 131.915 28.1335C131.871 26.2417 132.23 24.3622 132.968 22.6196C133.706 20.877 134.806 19.3111 136.194 18.0257C138.715 15.605 142.05 14.2131 145.543 14.1229C149.037 14.0327 152.439 15.2508 155.081 17.5384C156.656 18.8458 157.912 20.4954 158.753 22.3615C159.594 24.2277 159.999 26.2612 159.935 28.3071C159.935 30.0246 159.597 31.7253 158.939 33.3119C158.282 34.8985 157.318 36.3399 156.103 37.5536L160.496 41.8865ZM149.38 30.804L153.265 34.6695C154.11 33.7928 154.771 32.7569 155.211 31.6223C155.651 30.4876 155.861 29.2767 155.829 28.0601C155.864 26.7246 155.629 25.3959 155.138 24.1536C154.647 22.9112 153.91 21.7807 152.972 20.8297C152.052 19.8786 150.946 19.1283 149.722 18.6262C148.498 18.1241 147.184 17.881 145.862 17.9122C144.543 17.8715 143.23 18.1105 142.011 18.6133C140.791 19.1162 139.692 19.8715 138.785 20.8297C137.854 21.8063 137.128 22.9589 136.648 24.22C136.169 25.4812 135.947 26.8253 135.994 28.1736C135.946 29.4899 136.178 30.8014 136.672 32.0221C137.167 33.2429 137.914 34.3453 138.865 35.257C139.763 36.1587 140.831 36.874 142.006 37.3621C143.182 37.8501 144.442 38.1013 145.715 38.1011C147.37 38.1228 149.002 37.7108 150.448 36.906L144.152 30.804H149.38Z"
          fill="#F9A719"
        />
        <path
          d="M164.982 41.3925V14.6209H168.988C170.428 14.585 171.867 14.724 173.274 15.0348C174.282 15.2793 175.216 15.7624 175.998 16.4435C176.77 17.1539 177.379 18.0242 177.78 18.9938C178.219 19.9748 178.446 21.037 178.448 22.1116C178.46 23.0363 178.268 23.9523 177.886 24.7946C177.504 25.6368 176.941 26.3847 176.238 26.9852C177.616 27.4338 178.803 28.3319 179.61 29.5355C180.569 30.988 180.996 32.7279 180.819 34.4597C180.641 36.1915 179.869 37.8084 178.635 39.0358C177.806 39.8945 176.775 40.5325 175.637 40.8918C174.145 41.2637 172.609 41.4344 171.071 41.3991L164.982 41.3925ZM169.028 26.1106H170.296C171.796 26.1106 172.896 25.779 173.594 25.1159C173.964 24.7262 174.249 24.2638 174.431 23.7581C174.612 23.2524 174.687 22.7144 174.649 22.1783C174.686 21.6508 174.609 21.1216 174.423 20.6264C174.238 20.1311 173.948 19.6814 173.574 19.3076C172.693 18.599 171.578 18.2486 170.45 18.3262H169.028V26.1106ZM169.028 37.5937H171.525C173.354 37.5937 174.689 37.2332 175.53 36.5188C175.962 36.1598 176.306 35.7078 176.537 35.1967C176.768 34.6856 176.881 34.1287 176.866 33.568C176.876 33.0197 176.767 32.4758 176.546 31.9742C176.324 31.4726 175.996 31.0254 175.584 30.6638C174.738 29.8894 173.227 29.5021 171.051 29.5021H169.008L169.028 37.5937Z"
          fill="#F9A719"
        />
      </g>
      <defs>
        <clipPath id="clip0_1_31">
          <rect width="180.892" height="56" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}
