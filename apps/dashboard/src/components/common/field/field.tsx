import { cn } from "@/lib/utils"
import { Form } from "@/components/common/form"
import { ReactElement, cloneElement } from "react"
import { ControllerRenderProps, FieldPath, FieldValues, UseControllerProps } from "react-hook-form"

export type FieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = Omit<UseControllerProps<TFieldValues, TName>, "name"> & {
  name: TName
  label?: string
  description?: string
  className?: string
  isCheckbox?: boolean
  children: ReactElement<{
    field: ControllerRenderProps<TFieldValues, TName>
  }>
  messageDelimiter?: string
  hideErrorMessage?: boolean
}
export const Field = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>(
  props: FieldProps<TFieldValues, TName>,
) => {
  return (
    <Form.Field
      control={props.control}
      name={props.name}
      render={({ field }: { field: any }) => {
        return (
          <Form.Item
            className={cn(props.className, props.isCheckbox ? "flex flex-row items-center gap-x-3 gap-y-0" : "")}
          >
            {!props.isCheckbox && props.label != null && <Form.Label>{props.label}</Form.Label>}
            <Form.Control>
              {cloneElement(props.children, {
                ...field,
                ...props.children.props,
              })}
            </Form.Control>
            {props.isCheckbox && <Form.Label className="text-sm font-normal">{props.label}</Form.Label>}
            {props.description && <Form.Hint>{props.description}</Form.Hint>}
            {!props.hideErrorMessage && <Form.ErrorMessage />}
          </Form.Item>
        )
      }}
    />
  )
}
