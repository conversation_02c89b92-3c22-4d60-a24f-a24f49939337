import { RouteDrawer, RouteFocusModal } from "@/components/modals"
import { ExclamationCircle } from "@medusajs/icons"
import { Button, Text, clx } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"

export type ErrorResultProps = {
  title?: string
  message?: string
  className?: string
  buttonLabel?: string
  buttonAction?: () => void
}

export const ErrorResult = ({ title, message, className, buttonLabel, buttonAction }: ErrorResultProps) => {
  const { t } = useTranslation()

  return (
    <div className={clx("flex h-[400px] w-full flex-col items-center justify-center gap-y-4", className)}>
      <div className="flex flex-col items-center gap-y-2">
        <ExclamationCircle />
        <Text size="small" leading="compact" weight="plus">
          {title ?? t("errorBoundary.defaultTitle")}
        </Text>
        <Text size="small" className="text-ui-fg-subtle">
          {message ?? t("errorBoundary.defaultMessage")}
        </Text>
      </div>

      {buttonLabel != null && buttonAction && (
        <Button variant="secondary" size="small" onClick={buttonAction}>
          {buttonLabel}
        </Button>
      )}
    </div>
  )
}

export const RouteFocusModalError = ({ title, message }: { title?: string; message?: string }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()

  return (
    <div className="flex h-full flex-col overflow-hidden">
      <RouteFocusModal.Header />
      <RouteFocusModal.Body>
        <ErrorResult
          title={title}
          message={message}
          buttonAction={() => navigate("./..")}
          buttonLabel={t("actions.back")}
        />
      </RouteFocusModal.Body>
      <RouteFocusModal.Footer>
        <div className="flex items-center justify-end gap-x-2">
          <RouteFocusModal.Close asChild>
            <Button size="small" variant="secondary">
              {t("actions.cancel")}
            </Button>
          </RouteFocusModal.Close>
        </div>
      </RouteFocusModal.Footer>
    </div>
  )
}

export const RouteDrawerError = ({ title, message }: { title?: string; message?: string }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()

  return (
    <div className="flex h-full flex-col overflow-hidden">
      <RouteDrawer.Header />
      <RouteDrawer.Body>
        <ErrorResult
          title={title}
          message={message}
          buttonAction={() => navigate("./..")}
          buttonLabel={t("actions.back")}
        />
      </RouteDrawer.Body>
      <RouteDrawer.Footer>
        <div className="flex items-center justify-end gap-x-2">
          <RouteDrawer.Close asChild>
            <Button size="small" variant="secondary">
              {t("actions.cancel")}
            </Button>
          </RouteDrawer.Close>
        </div>
      </RouteDrawer.Footer>
    </div>
  )
}
