import { useSession } from "@/hooks/auth"
import { SearchProvider } from "@/providers/search-provider"
import { SidebarProvider } from "@/providers/sidebar-provider"
import { Spinner } from "@medusajs/icons"
import { Navigate, Outlet, useLocation } from "react-router-dom"

export const ProtectedRoute = () => {
  const { data: user, isLoading, error } = useSession()

  const location = useLocation()

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Spinner className="animate-spin text-ui-fg-interactive" />
      </div>
    )
  }

  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  if (error) {
    throw error
  }

  // if (!session.data?.user.role) {
  //   return (
  //     <div className="p-6">
  //       <h1>Error</h1>
  //       <p>This page is private.</p>
  //       <div className="mt-8"></div>
  //     </div>
  //   );
  // }

  return (
    <SidebarProvider>
      <SearchProvider>
        <Outlet />
      </SearchProvider>
    </SidebarProvider>
  )
}
