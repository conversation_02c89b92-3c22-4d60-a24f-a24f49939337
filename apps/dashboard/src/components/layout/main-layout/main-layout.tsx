import { CogSixTooth, EllipsisHorizontal } from "@medusajs/icons"
import { Avatar, Divider, DropdownMenu, Text, clx } from "@saf/ui"
import { IconLayoutGrid, IconUsers } from "@tabler/icons-react"
import { useTranslation } from "react-i18next"

import { Skeleton } from "../../common/skeleton"
import { INavItem, NavItem } from "../../layout/nav-item"
import { Shell } from "../../layout/shell"

import { LogoText } from "@/components/common/logo/logo-text"
import { useSession } from "@/hooks/auth"
import { useLocation, useParams } from "react-router-dom"
import { UserMenu } from "../user-menu"

export const MainLayout = () => {
  return (
    <Shell>
      <MainSidebar />
    </Shell>
  )
}

const MainSidebar = () => {
  return (
    <aside className="flex flex-1 flex-col justify-between overflow-y-auto">
      <div className="flex flex-1 flex-col">
        <div className="sticky top-0 bg-ui-bg-subtle">
          <div className="flex h-[var(--topbar-height)] items-center px-3">
            <LogoText className="h-8 w-auto" />
          </div>
          <div className="px-3">
            <Divider variant="dashed" />
          </div>
          <TeamSelector />
          <div className="px-3">
            <Divider variant="dashed" />
          </div>
        </div>
        <div className="flex flex-1 flex-col justify-between">
          <div className="flex flex-1 flex-col">
            <CoreRouteSection />
          </div>
          <UtilitySection />
        </div>
        <div className="sticky bottom-0 bg-ui-bg-subtle">
          <UserSection />
        </div>
      </div>
    </aside>
  )
}

// const Logout = () => {
//   const { t } = useTranslation()
//   // const navigate = useNavigate();

//   // const { mutateAsync: logoutMutation } = useLogout()

//   const handleLogout = async () => {
//     // await logoutMutation(undefined, {
//     //   onSuccess: () => {
//     //     /**
//     //      * When the user logs out, we want to clear the query cache
//     //      */
//     //     queryClient.clear()
//     //     navigate("/login")
//     //   },
//     // })
//   }

//   return (
//     <DropdownMenu.Item onClick={handleLogout}>
//       <div className="flex items-center gap-x-2">
//         <OpenRectArrowOut className="text-ui-fg-subtle" />
//         <span>{t("app.menus.actions.logout")}</span>
//       </div>
//     </DropdownMenu.Item>
//   )
// }

const TeamSelector = () => {
  const { teamId = "" } = useParams()
  const { data: user, isLoading } = useSession()
  // const navigate = useNavigate()

  const currentTeam = user?.teams?.find((team) => team.id?.toString() === teamId)

  const name = currentTeam?.name || "No Team Selected"
  const fallback = currentTeam?.name?.slice(0, 1)?.toUpperCase()

  return (
    <div className="w-full p-3">
      <DropdownMenu>
        <DropdownMenu.Trigger
          disabled={isLoading}
          className={clx(
            "grid w-full grid-cols-[24px_1fr_15px] items-center gap-x-3 rounded-md bg-ui-bg-subtle p-0.5 pr-2 outline-none transition-fg",
            "hover:bg-ui-bg-subtle-hover",
            "data-[state=open]:bg-ui-bg-subtle-hover",
            "focus-visible:shadow-borders-focus",
          )}
        >
          {fallback ? (
            <Avatar variant="squared" size="xsmall" fallback={fallback} />
          ) : (
            <Skeleton className="h-6 w-6 rounded-md" />
          )}
          <div className="block overflow-hidden text-left">
            {name ? (
              <Text size="small" weight="plus" leading="compact" className="truncate">
                {name}
              </Text>
            ) : (
              <Skeleton className="h-[9px] w-[120px]" />
            )}
          </div>
          <EllipsisHorizontal className="text-ui-fg-muted" />
        </DropdownMenu.Trigger>
        {!isLoading && (
          <DropdownMenu.Content className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-0">
            <div className="flex items-center gap-x-3 px-2 py-1">
              {fallback != null ? <Avatar variant="squared" size="small" fallback={fallback} /> : null}
              <div className="flex flex-col overflow-hidden">
                <Text size="small" weight="plus" leading="compact" className="truncate">
                  {name}
                </Text>
              </div>
            </div>
            {/* <DropdownMenu.Separator /> */}
            {/* <DropdownMenu.RadioGroup
              value={teamId || "none"}
              onValueChange={(v) => navigate(`/teams/${v}`)}
            >
              <DropdownMenu.RadioItem value="none">
                No Team Selected
              </DropdownMenu.RadioItem>
              <DropdownMenu.Separator />
              {user?.teams?.map((team) => (
                <DropdownMenu.RadioItem value={team.id?.toString()}>
                  {team.name}
                </DropdownMenu.RadioItem>
              ))}
            </DropdownMenu.RadioGroup> */}
          </DropdownMenu.Content>
        )}
      </DropdownMenu>
    </div>
  )
}

const useCoreRoutes = (): Omit<INavItem, "pathname">[] => {
  const { teamId } = useParams()
  return [
    {
      icon: <IconLayoutGrid className="size-4" />,
      label: "Mini Apps",
      to: teamId != null ? `/teams/${teamId}/mini-apps` : "/mini-apps",
    },
    {
      icon: <IconUsers className="size-4" />,
      label: "Users",
      to: teamId != null ? `/teams/${teamId}/users` : "/users",
    },
  ]
}

const CoreRouteSection = () => {
  const coreRoutes = useCoreRoutes()

  return (
    <nav className="flex flex-col gap-y-1 py-3">
      {coreRoutes.map((route) => {
        return <NavItem key={route.to} {...route} />
      })}
    </nav>
  )
}

const UtilitySection = () => {
  const location = useLocation()
  const { t } = useTranslation()

  return (
    <div className="flex flex-col gap-y-0.5 py-3">
      <NavItem label={t("app.nav.settings.header")} to="/settings" from={location.pathname} icon={<CogSixTooth />} />
    </div>
  )
}

const UserSection = () => {
  return (
    <div>
      <div className="px-3">
        <Divider variant="dashed" />
      </div>
      <UserMenu />
    </div>
  )
}
