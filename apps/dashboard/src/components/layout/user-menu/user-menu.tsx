import {
  BookOpen,
  CircleHalfSolid,
  EllipsisHorizontal,
  OpenRectArrowOut,
  TimelineVertical,
  User as UserIcon,
} from "@medusajs/icons"
import { Avatar, DropdownMenu, Text, clx, toast } from "@saf/ui"
import { useTranslation } from "react-i18next"

import { Skeleton } from "../../common/skeleton"

import { useLogout, useSession } from "@/hooks/auth"
import { useState } from "react"
import { Link, useLocation, useNavigate } from "react-router-dom"
import { useTheme } from "../../../providers/theme-provider"

export const UserMenu = () => {
  const { t } = useTranslation()
  const location = useLocation()

  const [openMenu, setOpenMenu] = useState(false)
  // const [openModal, setOpenModal] = useState(false)

  // const toggleModal = () => {
  //   setOpenMenu(false)
  //   setOpenModal(!openModal)
  // }

  return (
    <DropdownMenu open={openMenu} onOpenChange={setOpenMenu}>
      <UserBadge />
      <DropdownMenu.Content className="min-w-[var(--radix-dropdown-menu-trigger-width)] max-w-[var(--radix-dropdown-menu-trigger-width)]">
        <UserItem />
        <DropdownMenu.Separator />
        <DropdownMenu.Item asChild>
          <Link to="/settings/profile" state={{ from: location.pathname }}>
            <UserIcon className="mr-2 text-ui-fg-subtle" />
            {t("app.menus.user.profileSettings")}
          </Link>
        </DropdownMenu.Item>
        <DropdownMenu.Separator />
        <DropdownMenu.Item asChild>
          <Link to="https://docs.medusajs.com" target="_blank">
            <BookOpen className="mr-2 text-ui-fg-subtle" />
            {t("app.menus.user.documentation")}
          </Link>
        </DropdownMenu.Item>
        <DropdownMenu.Item asChild>
          <Link to="https://medusajs.com/changelog/" target="_blank">
            <TimelineVertical className="mr-2 text-ui-fg-subtle" />
            {t("app.menus.user.changelog")}
          </Link>
        </DropdownMenu.Item>
        <DropdownMenu.Separator />
        <ThemeToggle />
        <DropdownMenu.Separator />
        <Logout />
      </DropdownMenu.Content>
    </DropdownMenu>
  )
}

const UserBadge = () => {
  const { data: user, isLoading, error } = useSession()

  if (isLoading) {
    return (
      <button className="flex max-w-[192px] select-none items-center gap-x-2 overflow-hidden text-ellipsis whitespace-nowrap rounded-full py-1 pl-1 pr-2.5 shadow-borders-base">
        <Skeleton className="h-5 w-5 rounded-full" />
        <Skeleton className="h-[9px] w-[70px]" />
      </button>
    )
  }

  if (error != null) {
    return null
  }

  const displayName = user?.name || user?.email

  const fallback = displayName ? displayName[0].toUpperCase() : null

  return (
    <div className="p-3">
      <DropdownMenu.Trigger
        disabled={!user}
        className={clx(
          "grid w-full cursor-pointer grid-cols-[24px_1fr_15px] items-center gap-2 rounded-md bg-ui-bg-subtle py-1 pl-0.5 pr-2 outline-none",
          "hover:bg-ui-bg-subtle-hover",
          "data-[state=open]:bg-ui-bg-subtle-hover",
          "focus-visible:shadow-borders-focus",
        )}
      >
        <div className="flex size-6 items-center justify-center">
          {fallback ? <Avatar size="xsmall" fallback={fallback} /> : <Skeleton className="h-6 w-6 rounded-full" />}
        </div>
        <div className="flex items-center overflow-hidden">
          {displayName ? (
            <Text size="xsmall" weight="plus" leading="compact" className="truncate">
              {displayName}
            </Text>
          ) : (
            <Skeleton className="h-[9px] w-[70px]" />
          )}
        </div>
        <EllipsisHorizontal className="text-ui-fg-muted" />
      </DropdownMenu.Trigger>
    </div>
  )
}

export const ThemeToggle = () => {
  const { t } = useTranslation()
  const { theme, setTheme } = useTheme()

  return (
    <DropdownMenu.SubMenu>
      <DropdownMenu.SubMenuTrigger className="rounded-md">
        <CircleHalfSolid className="mr-2 text-ui-fg-subtle" />
        {t("app.menus.user.theme.label")}
      </DropdownMenu.SubMenuTrigger>
      <DropdownMenu.SubMenuContent>
        <DropdownMenu.RadioGroup value={theme}>
          <DropdownMenu.RadioItem
            value="system"
            onClick={(e) => {
              e.preventDefault()
              setTheme("system")
            }}
          >
            {t("app.menus.user.theme.system")}
          </DropdownMenu.RadioItem>
          <DropdownMenu.RadioItem
            value="light"
            onClick={(e) => {
              e.preventDefault()
              setTheme("light")
            }}
          >
            {t("app.menus.user.theme.light")}
          </DropdownMenu.RadioItem>
          <DropdownMenu.RadioItem
            value="dark"
            onClick={(e) => {
              e.preventDefault()
              setTheme("dark")
            }}
          >
            {t("app.menus.user.theme.dark")}
          </DropdownMenu.RadioItem>
        </DropdownMenu.RadioGroup>
      </DropdownMenu.SubMenuContent>
    </DropdownMenu.SubMenu>
  )
}

const Logout = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { mutate: logoutMutation } = useLogout()

  const handleLogout = async () => {
    logoutMutation(undefined, {
      onSuccess: () => {
        navigate("/login")
      },
      onError: (error) => {
        toast.error(error.message)
      },
    })
  }

  return (
    <DropdownMenu.Item onClick={handleLogout}>
      <div className="flex items-center gap-x-2">
        <OpenRectArrowOut className="text-ui-fg-subtle" />
        <span>{t("app.menus.actions.logout")}</span>
      </div>
    </DropdownMenu.Item>
  )
}

export const UserItem = () => {
  const { data: user, isLoading, error } = useSession()

  const loaded = !isLoading && !!user

  if (!loaded) {
    return <div></div>
  }

  const name = user.name
  const email = user.email
  const fallback = name ? name[0].toUpperCase() : email[0].toUpperCase()

  if (error != null) {
    throw error
  }

  return (
    <div className="flex items-center gap-x-3 overflow-hidden px-2 py-1">
      <Avatar size="small" variant="rounded" fallback={fallback} />
      <div className="block w-full min-w-0 max-w-[187px] overflow-hidden whitespace-nowrap">
        <Text size="small" weight="plus" leading="compact" className="overflow-hidden text-ellipsis whitespace-nowrap">
          {name || email}
        </Text>
        {!!name && (
          <Text
            size="xsmall"
            leading="compact"
            className="overflow-hidden text-ellipsis whitespace-nowrap text-ui-fg-subtle"
          >
            {email}
          </Text>
        )}
      </div>
    </div>
  )
}
