import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Checkbox, Input, Text } from "@saf/ui"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useDeepCompareEffect } from "use-deep-compare"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"

const textEntryEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    label: z.string().min(1, "Label is required"),
    required: z.boolean(),
    binding: z.string().optional(),
  }),
)

type TextEntryEditorSchema = z.infer<typeof textEntryEditorSchema>

export const TextEntryEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["TextEntryWidget"]
  onUpdate: (updatedData: components["schemas"]["TextEntryWidget"]) => void
}) => {
  const [initialRender, setInitialRender] = useState(true)
  const form = useForm<TextEntryEditorSchema>({
    resolver: zodResolver(textEntryEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      label: "",
      required: false,
      binding: "",
    },
  })

  useDeepCompareEffect(() => {
    if (data && initialRender) {
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        label: data.config.label || "",
        required: data.config.required ?? false,
        binding: data.config.binding || "",
      })
      setInitialRender(false)
    }
  }, [data, form, initialRender])

  useEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback({ values }) {
        const updatedData: components["schemas"]["TextEntryWidget"] = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            label: values.label || "",
            required: values.required ?? false,
            binding: values.binding || "",
          },
        }
        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />
        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Configuration
          </Text>
          <Field control={form.control} name="label" label="Label">
            <Input placeholder="Enter field label..." />
          </Field>
          <Field control={form.control} name="binding" label="Data Binding">
            <Input placeholder="Enter data binding field..." />
          </Field>
          <Form.Field
            control={form.control}
            name="required"
            render={({ field: { value, onChange, ...field } }) => {
              return (
                <Form.Item className="flex flex-row items-center gap-3">
                  <Form.Control>
                    <Checkbox checked={value} onCheckedChange={onChange} {...field} />
                  </Form.Control>
                  <Form.Label>Required Field</Form.Label>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
        </div>
      </form>
    </Form>
  )
}
