import { components } from "@saf/sdk"
import { Input, Label, Text } from "@saf/ui"

export const TextEntryWidget = ({ data }: { data: components["schemas"]["TextEntryWidget"] }) => {
  const label = data.config.label || data.name || "Text Entry"
  const isRequired = data.config.required || false

  return (
    <div className="px-4 py-1.5">
      <Label className="space-y-2">
        <div className="flex items-center gap-1">
          <Text size="small" weight="plus">
            {label}
          </Text>
          {isRequired && (
            <Text size="small" className="text-ui-button-danger">
              *
            </Text>
          )}
        </div>
        <Input placeholder={`Enter ${label.toLowerCase()}...`} required={isRequired} />
      </Label>
    </div>
  )
}
