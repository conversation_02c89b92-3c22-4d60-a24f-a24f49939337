import { cn } from "@/lib/utils"
import { components } from "@saf/sdk"
import { cva, type VariantProps } from "class-variance-authority"

const separatorVariants = cva("w-full", {
  variants: {
    size: {
      small: "py-2",
      medium: "py-4",
      large: "py-8",
    },
    drawLine: {
      true: "relative before:absolute before:top-1/2 before:left-0 before:right-0 before:h-px before:bg-ui-border-base before:-translate-y-1/2",
      false: "",
    },
  },
  defaultVariants: {
    size: "medium",
    drawLine: false,
  },
})

export interface SeparatorWidgetProps extends VariantProps<typeof separatorVariants> {
  size: "small" | "medium" | "large"
  drawLine: boolean
}

export const SeparatorWidget = ({ data }: { data: components["schemas"]["SeparatorWidget"] }) => {
  return (
    <div className="px-4">
      <div
        className={cn(
          separatorVariants({
            size: data.config.design?.size,
            drawLine: data.config.design?.drawLine,
          }),
        )}
      />
    </div>
  )
}
