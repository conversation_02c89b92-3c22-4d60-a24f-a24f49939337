import { cn } from "@/lib/utils"
import { components } from "@saf/sdk"
import { cva, type VariantProps } from "class-variance-authority"

const titleVariants = cva("", {
  variants: {
    style: {
      simple: "flex items-start gap-3",
      banner: "flex flex-col gap-2 bg-ui-bg-subtle",
    },
  },
  defaultVariants: {
    style: "simple",
  },
})

const imageVariants = cva("flex-shrink-0 overflow-hidden", {
  variants: {
    style: {
      simple: "h-14 w-14 mt-0.5",
      banner: "w-full h-auto",
    },
    imageFill: {
      fill: "object-cover",
      contain: "object-contain",
    },
  },
  defaultVariants: {
    style: "simple",
    imageFill: "contain",
  },
})

export interface TitleWidgetProps extends VariantProps<typeof titleVariants> {
  data?: {
    title?: string
    subtitle?: string
    meta?: string
    image?: string
  }
}

export const TitleWidget = ({ data }: { data: components["schemas"]["TitleWidget"] }) => {
  const { style, data: titleData, design } = data.config
  const { title, subtitle, meta, image } = titleData || {}
  const { imageFill } = design || {}

  if (!title && !subtitle && !meta && !image) {
    return null
  }

  return (
    <div className="px-4 py-0.5">
      <div
        className={cn(
          titleVariants({
            style,
          }),
        )}
      >
        {/* Image - positioned on the left for Simple style, on top for Banner style */}
        {image && (
          <div
            className={cn(
              imageVariants({
                style,
                imageFill,
              }),
            )}
          >
            <img
              src={image}
              alt={title || "Title image"}
              className={cn(
                style === "banner" ? "h-auto w-full" : "h-full w-full",
                imageFill === "fill" ? "object-cover" : "object-contain",
              )}
              onError={(e) => {
                // Handle broken images gracefully
                const target = e.target as HTMLImageElement
                target.style.display = "none"
                const parent = target.parentElement
                if (parent) {
                  parent.innerHTML = `
                    <div class="flex h-full w-full items-center justify-center bg-ui-bg-subtle text-ui-text-muted">
                      <div class="h-6 w-6">
                        <svg stroke="currentColor" fill="none" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    </div>
                  `
                }
              }}
            />
          </div>
        )}

        {/* Content */}
        <div className={cn("flex flex-col gap-0.5", style === "simple" ? "min-w-0 flex-1" : "")}>
          {meta && <p className="text-[10px] uppercase text-ui-fg-subtle">{meta}</p>}
          {title && <h3 className="text-lg font-semibold leading-tight tracking-tight text-ui-fg-base">{title}</h3>}
          {subtitle && <p className="text-sm text-ui-fg-subtle">{subtitle}</p>}
        </div>
      </div>
    </div>
  )
}
