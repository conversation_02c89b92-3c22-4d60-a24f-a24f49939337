import { cn } from "@/lib/utils"
import { components } from "@saf/sdk"
import { IconPhoto } from "@tabler/icons-react"
import { cva, type VariantProps } from "class-variance-authority"

const imageVariants = cva("overflow-hidden", {
  variants: {
    aspectRatio: {
      "1:1": "aspect-square",
      "16:9": "aspect-video",
      "4:3": "aspect-[4/3]",
      "3:2": "aspect-[3/2]",
      auto: "",
    },
    fill: {
      fill: "object-cover",
      contain: "object-contain",
    },
    fullWidth: {
      true: "w-full",
      false: "max-w-md mx-auto",
    },
  },
  defaultVariants: {
    aspectRatio: "1:1",
    fill: "fill",
    fullWidth: false,
  },
})

const imageContainerVariants = cva("", {
  variants: {
    fullWidth: {
      true: "w-full",
      false: "w-fit mx-auto",
    },
  },
  defaultVariants: {
    fullWidth: false,
  },
})

export interface ImageWidgetProps extends VariantProps<typeof imageVariants> {
  content: string
  alt?: string
}

export const ImageWidget = ({ data }: { data: components["schemas"]["ImageWidget"] }) => {
  const { content, design } = data.config

  if (!content) {
    return (
      <div className="px-4 py-2">
        <div className="flex items-center justify-center border border-dashed border-ui-border-base bg-ui-bg-subtle p-8 text-center">
          <div className="text-ui-text-muted">
            <IconPhoto className="mx-auto" />
            <p className="mt-2 text-sm">No image selected</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("py-0.5", !data.config.design.fullWidth && "px-4")}>
      <div
        className={cn(
          imageContainerVariants({
            fullWidth: design?.fullWidth,
          }),
        )}
      >
        <div
          className={cn(
            imageVariants({
              aspectRatio: design?.aspectRatio,
              fill: design?.fill,
              fullWidth: design?.fullWidth,
            }),
          )}
        >
          <img
            src={content}
            alt={data.name || "Image"}
            className={cn("h-full w-full", design?.fill === "fill" ? "object-cover" : "object-contain")}
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.style.display = "none"
              const parent = target.parentElement
              if (parent) {
                parent.innerHTML = `
                  <div class="flex h-full w-full items-center justify-center text-ui-text-muted">
                    <div class="text-center">
                      <svg class="mx-auto h-12 w-12" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                      <p class="mt-2 text-sm">Failed to load image</p>
                    </div>
                  </div>
                `
              }
            }}
          />
        </div>
      </div>
    </div>
  )
}
