import { cn } from "@/lib/utils"
import { ArrowLeft, ArrowUpRightOnBox } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { Button } from "@saf/ui"

// Map action types to appropriate icons
const getActionIcon = (actionType: components["schemas"]["ActionType"]) => {
  // Using default icons based on action type
  switch (actionType) {
    case "navigate_back":
      return <ArrowLeft />
    case "open_url":
      return <ArrowUpRightOnBox />
    default:
      return null
  }
}

// Map action types to button variants
const getButtonVariant = (actionType: components["schemas"]["ActionType"], isPrimary: boolean) => {
  if (isPrimary) {
    return "primary"
  }

  switch (actionType) {
    case "submit_form":
      return "primary"
    default:
      return "secondary"
  }
}

export const ButtonWidget = ({ data }: { data: components["schemas"]["ButtonWidget"] }) => {
  const { buttons, design } = data.config

  // Determine which button should be primary based on design.primary setting
  const getPrimaryIndex = () => {
    if (!design?.primary || design.primary === "none") return -1
    if (design.primary === "left") return 0
    if (design.primary === "right") return buttons.length - 1
    return -1
  }

  const primaryIndex = getPrimaryIndex()

  const handleButtonClick = (action: components["schemas"]["Action"]) => {
    // Handle different action types
    switch (action.actionType) {
      case "navigate_to_page":
        // This would typically be handled by the parent application
        console.log("Navigate to page:", action.title)
        break
      case "navigate_back":
        window.history.back()
        break
      case "open_url":
        // Assuming the title contains the URL for open_url actions
        window.open(action.title, "_blank")
        break
      case "submit_form":
        // This would typically trigger form submission in the parent
        console.log("Submit form:", action.title)
        break
      default:
        console.log("Unknown action type:", action.actionType)
    }
  }

  if (!buttons || buttons.length === 0) {
    return (
      <div className="px-4 py-2">
        <div className="text-sm italic text-ui-fg-subtle">No buttons configured</div>
      </div>
    )
  }

  return (
    <div className="px-4 py-2">
      <div className="flex flex-wrap gap-2">
        {buttons.map((button, index) => {
          const isPrimary = index === primaryIndex
          const variant = getButtonVariant(button.actionType, isPrimary)
          const icon = getActionIcon(button.actionType)

          return (
            <Button
              key={index}
              variant={variant}
              size="base"
              onClick={() => handleButtonClick(button)}
              className={cn(
                "flex items-center gap-2",
                // Add any additional styling based on action type
                {
                  "min-w-[100px]": button.actionType === "submit_form",
                },
              )}
            >
              {icon}
              <span>{button.title}</span>
            </Button>
          )
        })}
      </div>
    </div>
  )
}
