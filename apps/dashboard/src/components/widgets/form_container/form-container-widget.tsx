import { components } from "@saf/sdk"
import { Button, Text } from "@saf/ui"
import { renderWidget } from "../index"

export const FormContainerWidget = ({ data }: { data: components["schemas"]["FormContainerWidget"] }) => {
  const { title, subtitle, widgets, design } = data.config
  const submitButtonText = design?.submitButtonText || "Submit"

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // TODO: Implement form submission logic
    console.log("Form submitted")
  }

  return (
    <div className="py-2">
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Form Header */}
        {(title || subtitle) && (
          <div className="space-y-1">
            {title && (
              <Text size="large" weight="plus" className="text-ui-text-base">
                {title}
              </Text>
            )}
            {subtitle && (
              <Text size="base" className="text-ui-text-muted">
                {subtitle}
              </Text>
            )}
          </div>
        )}

        {/* Form Fields */}
        <div className="space-y-3">{widgets?.map((widget) => <div key={widget.id}>{renderWidget(widget)}</div>)}</div>

        {/* Submit Button */}
        <div className="px-4 pt-2">
          <Button type="submit" className="w-full">
            {submitButtonText}
          </Button>
        </div>
      </form>
    </div>
  )
}
