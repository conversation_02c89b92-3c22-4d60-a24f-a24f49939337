// TODO: remove this file when it's ready to make the CollectionWidget component production-ready
import { safQuery } from "@/client"
import { components } from "@saf/sdk"
import { Liquid } from "liquidjs"
import React from "react"

// Initialize Liquid engine
const engine = new Liquid()

// Utility function to render liquid templates with data
const renderLiquidTemplate = async (template: string, data: any): Promise<string> => {
  try {
    if (!template || typeof template !== "string") {
      return ""
    }

    // If template doesn't contain liquid syntax, return as-is
    if (!template.includes("{{") && !template.includes("{%")) {
      return template
    }

    const result = await engine.parseAndRender(template, data)
    return result || ""
  } catch (error) {
    console.warn("Failed to render template:", template, error)
    return template // Return original template if parsing fails
  }
}

// Mock data for demonstration - in real implementation this would come from the data source
const generateMockData = (count: number = 5) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    title: `Item ${i + 1}`,
    subtitle: `This is the subtitle for item ${i + 1}`,
    meta: `Category ${(i % 3) + 1}`,
    image: `https://picsum.photos/200/200?random=${i + 1}`,
    // Additional fields for more complex liquid template examples
    price: 19.99 + i * 5,
    currency: "USD",
    status: i % 2 === 0 ? "active" : "inactive",
    tags: [`tag${i + 1}`, `category${(i % 3) + 1}`],
    author: {
      name: `Author ${i + 1}`,
      email: `author${i + 1}@example.com`,
    },
    createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
  }))
}

const CollectionItem = ({
  item,
  itemsData,
}: {
  item: any
  itemsData: components["schemas"]["CollectionWidget"]["config"]["itemsData"]
}) => {
  // Example liquid templates that could be used:
  // title: "{{title | upcase}}" - uppercase the title
  // subtitle: "{{subtitle | truncate: 50}}" - truncate subtitle to 50 chars
  // meta: "{{status | capitalize}} - {{price}} {{currency}}" - combine multiple fields
  // image: "{{image}}?size=200x200" - add query parameters to image URL
  const [renderedData, setRenderedData] = React.useState<{
    title: string
    subtitle: string
    meta: string
    image: string
  }>({
    title: "",
    subtitle: "",
    meta: "",
    image: "",
  })

  // Render liquid templates when item or itemsData changes
  React.useEffect(() => {
    const renderTemplates = async () => {
      try {
        const [title, subtitle, meta, image] = await Promise.all([
          renderLiquidTemplate(itemsData.title, item),
          renderLiquidTemplate(itemsData.subtitle, item),
          renderLiquidTemplate(itemsData.meta, item),
          renderLiquidTemplate(itemsData.image, item),
        ])

        setRenderedData({
          title,
          subtitle,
          meta,
          image,
        })
      } catch (error) {
        console.warn("Failed to render collection item templates:", error)
        // Fallback to direct item properties
        setRenderedData({
          title: item.title || "",
          subtitle: item.subtitle || "",
          meta: item.meta || "",
          image: item.image || "",
        })
      }
    }

    renderTemplates()
  }, [item, itemsData])

  return (
    <div>
      {renderedData.image && (
        <div>
          <img
            src={renderedData.image}
            alt={renderedData.title || "Collection item"}
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.style.display = "none"
            }}
          />
        </div>
      )}
      <div>
        {renderedData.meta && <p>{renderedData.meta}</p>}
        {renderedData.title && <h4>{renderedData.title}</h4>}
        {renderedData.subtitle && <p>{renderedData.subtitle}</p>}
      </div>
    </div>
  )
}

export const CollectionWidgetPoc = ({ data }: { data: components["schemas"]["CollectionWidget"] }) => {
  const { config } = data
  const { itemsData, options, pagination } = config

  const mockItems = generateMockData(options?.limitItems || 5)

  const externalApiData = safQuery.useQuery(
    "post",
    "/api/customers/external-api/{externalApiId}",
    {
      params: {
        path: {
          externalApiId: config.bindingConfig.externalApiId || 0,
        },
      },
    },
    {
      enabled: config.bindingConfig.externalApiId != null,
    },
  )

  if (!itemsData?.title && !itemsData?.subtitle && !itemsData?.meta && !itemsData?.image) {
    return <div>No collection configuration</div>
  }

  if (!mockItems || mockItems.length === 0) {
    return <div>No items to display</div>
  }

  return (
    <div>
      <div>
        {mockItems.map((item) => (
          <CollectionItem key={item.id} item={item} itemsData={itemsData} />
        ))}
      </div>

      {pagination?.enabled && (
        <div>
          {pagination.page || "1"} / {pagination.limit || "10"}
        </div>
      )}
    </div>
  )
}
