import { safQuery } from "@/client"
import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Options } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Checkbox, Input, Label, Select, Text } from "@saf/ui"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useParams } from "react-router-dom"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"
import { useDeepCompareEffect } from "use-deep-compare"

const styleOptions: Options = [
  { label: "List", value: "list" },
  { label: "Grid", value: "grid" },
]

const sizeOptions: Options = [
  { label: "Default", value: "default" },
  { label: "Compact", value: "compact" },
]

const designStyleOptions: Options = [
  { label: "Default", value: "default" },
  { label: "Card", value: "card" },
]

const imageShapeOptions: Options = [
  { label: "Circle", value: "circle" },
  { label: "Square", value: "square" },
]

const collectionEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    dataSourceId: z.string().optional(),
    externalApiId: z.string().optional(),
    style: z.enum(createEnumFromOptions(styleOptions)),
    titleField: z.string().optional(),
    subtitleField: z.string().optional(),
    metaField: z.string().optional(),
    imageField: z.string().optional(),
    size: z.enum(createEnumFromOptions(sizeOptions)),
    designStyle: z.enum(createEnumFromOptions(designStyleOptions)),
    imageShape: z.enum(createEnumFromOptions(imageShapeOptions)),
    limitItems: z.number().optional(),
    paginationEnabled: z.boolean(),
    pageField: z.string().optional(),
    limitField: z.string().optional(),
  }),
)

type CollectionEditorSchema = z.infer<typeof collectionEditorSchema>

export const CollectionEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["CollectionWidget"]
  onUpdate: (updatedData: components["schemas"]["CollectionWidget"]) => void
}) => {
  const [initialRender, setInitialRender] = useState(true)
  const { teamId = "", miniAppId = "" } = useParams()

  const form = useForm<CollectionEditorSchema>({
    resolver: zodResolver(collectionEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      dataSourceId: "",
      externalApiId: "",
      style: "list",
      titleField: "",
      subtitleField: "",
      metaField: "",
      imageField: "",
      size: "default",
      designStyle: "default",
      imageShape: "circle",
      limitItems: undefined,
      paginationEnabled: false,
      pageField: "",
      limitField: "",
    },
  })

  const { data: dataSourcesData } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
        },
        query: {
          page: "1",
          limit: "200",
        },
      },
    },
  )

  const selectedDataSourceId = form.watch("dataSourceId")

  const { data: externalApisData } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
          dataSourceId: parseInt(selectedDataSourceId ?? ""),
        },
        query: {
          page: "1",
          limit: "200",
        },
      },
    },
    {
      enabled: !!teamId && !!miniAppId && selectedDataSourceId != "" && selectedDataSourceId != null,
    },
  )

  useDeepCompareEffect(() => {
    if (data && initialRender) {
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        dataSourceId: data.config.bindingConfig?.dataSourceId?.toString() || undefined,
        externalApiId: data.config.bindingConfig?.externalApiId?.toString() || undefined,
        style: data.config.style || "list",
        titleField: data.config.itemsData?.title || "",
        subtitleField: data.config.itemsData?.subtitle || "",
        metaField: data.config.itemsData?.meta || "",
        imageField: data.config.itemsData?.image || "",
        size: data.config.design?.size || "default",
        designStyle: data.config.design?.style || "default",
        imageShape: data.config.design?.imageShape || "circle",
        limitItems: data.config.options?.limitItems || undefined,
        paginationEnabled: data.config.pagination?.enabled ?? false,
        pageField: data.config.pagination?.page || "",
        limitField: data.config.pagination?.limit || "",
      })
      setInitialRender(false)
    }
  }, [data, form, initialRender])

  useEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback({ values }) {
        const updatedData: components["schemas"]["CollectionWidget"] = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            bindingConfig: {
              dataSourceId: values.dataSourceId != null ? parseInt(values.dataSourceId) : 0,
              externalApiId: values.externalApiId != null ? parseInt(values.externalApiId) : 0,
            },
            style: values.style as components["schemas"]["CollectionWidget"]["config"]["style"],
            itemsData: {
              title: values.titleField || "",
              subtitle: values.subtitleField || "",
              meta: values.metaField || "",
              image: values.imageField || "",
            },
            design: {
              size: values.size as components["schemas"]["CollectionWidget"]["config"]["design"]["size"],
              style: values.designStyle as components["schemas"]["CollectionWidget"]["config"]["design"]["style"],
              imageShape:
                values.imageShape as components["schemas"]["CollectionWidget"]["config"]["design"]["imageShape"],
            },
            options: {
              limitItems: values.limitItems,
            },
            pagination: {
              enabled: values.paginationEnabled ?? false,
              page: values.pageField,
              limit: values.limitField,
            },
          },
        }
        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  const dataSourceOptions =
    dataSourcesData?.items?.map((ds) => ({
      label: ds.name,
      value: ds.id.toString(),
    })) || []

  const externalApiOptions =
    externalApisData?.items?.map((api) => ({
      label: api.name,
      value: api.id.toString(),
    })) || []

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Data Binding
          </Text>

          <Label className="flex items-center justify-between gap-4">
            <span>Data Source</span>
            <div className="w-full max-w-[170px]">
              <Field {...form} name="dataSourceId">
                <Select
                  onValueChange={(value) => {
                    form.setValue("dataSourceId", value != null ? value : undefined)
                    form.setValue("externalApiId", undefined)
                  }}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="Select data source..." />
                  </Select.Trigger>
                  <Select.Content>
                    {dataSourceOptions.map((item) => (
                      <Select.Item key={item.value} value={item.value}>
                        {item.label}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </Field>
            </div>
          </Label>

          {selectedDataSourceId && (
            <Label className="flex items-center justify-between gap-4">
              <span>External API</span>
              <div className="w-full max-w-[170px]">
                <Field {...form} name="externalApiId">
                  <Select onValueChange={(value) => form.setValue("externalApiId", value ? value : undefined)}>
                    <Select.Trigger>
                      <Select.Value placeholder="Select external API..." />
                    </Select.Trigger>
                    <Select.Content>
                      {externalApiOptions.map((item) => (
                        <Select.Item key={item.value} value={item.value}>
                          {item.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </Field>
              </div>
            </Label>
          )}
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Layout
          </Text>

          <Label className="flex items-center justify-between gap-4">
            <span>Style</span>
            <div className="w-full max-w-[170px]">
              <Field {...form} name="style">
                <Select onValueChange={form.setValue.bind(null, "style")}>
                  <Select.Trigger>
                    <Select.Value />
                  </Select.Trigger>
                  <Select.Content>
                    {styleOptions.map((item) => (
                      <Select.Item key={item.value} value={item.value}>
                        {item.label}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </Field>
            </div>
          </Label>
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Field Mapping
          </Text>

          <Field control={form.control} name="titleField" label="Title">
            <Input placeholder="e.g., title, name..." />
          </Field>

          <Field control={form.control} name="subtitleField" label="Subtitle">
            <Input placeholder="e.g., subtitle, description..." />
          </Field>

          <Field control={form.control} name="metaField" label="Meta">
            <Input placeholder="e.g., category, type..." />
          </Field>

          <Field control={form.control} name="imageField" label="Image">
            <Input placeholder="e.g., image, thumbnail..." />
          </Field>
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Design
          </Text>
          <div className="space-y-4">
            <Label className="flex items-center justify-between gap-4">
              <span>Size</span>
              <div className="w-full max-w-[170px]">
                <Field {...form} name="size">
                  <Select onValueChange={form.setValue.bind(null, "size")}>
                    <Select.Trigger>
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      {sizeOptions.map((item) => (
                        <Select.Item key={item.value} value={item.value}>
                          {item.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </Field>
              </div>
            </Label>

            <Label className="flex items-center justify-between gap-4">
              <span>Style</span>
              <div className="w-full max-w-[170px]">
                <Field {...form} name="designStyle">
                  <Select onValueChange={form.setValue.bind(null, "designStyle")}>
                    <Select.Trigger>
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      {designStyleOptions.map((item) => (
                        <Select.Item key={item.value} value={item.value}>
                          {item.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </Field>
              </div>
            </Label>

            <Label className="flex items-center justify-between gap-4">
              <span>Image Shape</span>
              <div className="w-full max-w-[170px]">
                <Field {...form} name="imageShape">
                  <Select onValueChange={form.setValue.bind(null, "imageShape")}>
                    <Select.Trigger>
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      {imageShapeOptions.map((item) => (
                        <Select.Item key={item.value} value={item.value}>
                          {item.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </Field>
              </div>
            </Label>
          </div>
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Options
          </Text>

          <Field control={form.control} name="limitItems" label="Limit Items">
            <Input type="number" placeholder="e.g., 10, 20..." />
          </Field>
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Pagination
          </Text>

          <Form.Field
            control={form.control}
            name="paginationEnabled"
            render={({ field: { value, onChange, ...field } }) => {
              return (
                <Form.Item className="flex flex-row items-center gap-3">
                  <Form.Control>
                    <Checkbox checked={value} onCheckedChange={onChange} {...field} />
                  </Form.Control>
                  <Form.Label>Enable Pagination</Form.Label>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />

          {form.watch("paginationEnabled") && (
            <div className="space-y-4">
              <Field control={form.control} name="pageField" label="Page">
                <Input placeholder="e.g., page, pageNumber..." />
              </Field>

              <Field control={form.control} name="limitField" label="Limit">
                <Input placeholder="e.g., limit, pageSize..." />
              </Field>
            </div>
          )}
        </div>
      </form>
    </Form>
  )
}
