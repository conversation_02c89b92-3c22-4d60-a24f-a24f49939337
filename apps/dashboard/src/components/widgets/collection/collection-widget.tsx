import { cn } from "@/lib/utils"
import { components } from "@saf/sdk"
import { IconDatabase } from "@tabler/icons-react"
import { cva, type VariantProps } from "class-variance-authority"

const collectionVariants = cva("", {
  variants: {
    style: {
      list: "flex flex-col gap-2",
      grid: "grid gap-3",
    },
    size: {
      default: "",
      compact: "gap-1",
    },
  },
  compoundVariants: [
    {
      style: "grid",
      size: "default",
      className: "grid-cols-1 sm:grid-cols-2",
    },
    {
      style: "grid",
      size: "compact",
      className: "grid-cols-2 sm:grid-cols-3",
    },
  ],
  defaultVariants: {
    style: "list",
    size: "default",
  },
})

const itemVariants = cva("", {
  variants: {
    style: {
      list: "flex items-start gap-3",
      grid: "flex flex-col gap-2",
    },
    designStyle: {
      default: "",
      card: "rounded-md border bg-ui-bg-subtle p-4",
    },
    size: {
      default: "py-3",
      compact: "py-2",
    },
  },
  compoundVariants: [
    {
      designStyle: "card",
      size: "default",
      className: "p-4",
    },
    {
      designStyle: "card",
      size: "compact",
      className: "p-3",
    },
  ],
  defaultVariants: {
    style: "list",
    designStyle: "default",
    size: "default",
  },
})

const imageVariants = cva("flex-shrink-0 overflow-hidden bg-ui-bg-subtle", {
  variants: {
    style: {
      list: "h-12 w-12",
      grid: "h-24 w-full",
    },
    imageShape: {
      circle: "rounded-full",
      square: "rounded-md",
    },
    size: {
      default: "",
      compact: "",
    },
  },
  compoundVariants: [
    {
      style: "list",
      size: "compact",
      className: "h-8 w-8",
    },
    {
      style: "grid",
      size: "compact",
      className: "h-16",
    },
    {
      style: "grid",
      imageShape: "circle",
      className: "h-24 w-24 mx-auto",
    },
    {
      style: "grid",
      imageShape: "circle",
      size: "compact",
      className: "h-16 w-16 mx-auto",
    },
  ],
  defaultVariants: {
    style: "list",
    imageShape: "square",
    size: "default",
  },
})

export interface CollectionWidgetProps extends VariantProps<typeof collectionVariants> {
  items?: any[]
}

// Mock data for demonstration - in real implementation this would come from the data source
const generateMockData = (count: number = 5) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    title: `Item ${i + 1}`,
    subtitle: `This is the subtitle for item ${i + 1}`,
    meta: `Category ${(i % 3) + 1}`,
    image: `https://picsum.photos/200/200?random=${i + 1}`,
  }))
}

const CollectionItem = ({
  item,
  style,
  design,
  itemsData: _itemsData,
}: {
  item: any
  style: "list" | "grid"
  design?: components["schemas"]["CollectionWidget"]["config"]["design"]
  itemsData: components["schemas"]["CollectionWidget"]["config"]["itemsData"]
}) => {
  return (
    <div
      className={cn(
        itemVariants({
          style,
          designStyle: design?.style,
          size: design?.size,
        }),
      )}
    >
      {/* Image */}
      {item.image && (
        <div
          className={cn(
            imageVariants({
              style,
              imageShape: design?.imageShape,
              size: design?.size,
            }),
          )}
        >
          <img
            src={item.image}
            alt={item.title || "Collection item"}
            className="h-full w-full object-cover"
            onError={(e) => {
              // Handle broken images gracefully
              const target = e.target as HTMLImageElement
              target.style.display = "none"
              const parent = target.parentElement
              if (parent) {
                parent.innerHTML = `
                  <div class="flex h-full w-full items-center justify-center text-ui-text-muted">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                `
              }
            }}
          />
        </div>
      )}

      {/* Content */}
      <div className={cn("flex min-w-0 flex-1 flex-col gap-1", style === "grid" && "text-center")}>
        {item.meta && <p className="text-ui-text-muted text-xs font-medium uppercase">{item.meta}</p>}
        {item.title && (
          <h4
            className={cn(
              "text-ui-text-base font-semibold leading-tight",
              design?.size === "compact" ? "text-sm" : "text-base",
            )}
          >
            {item.title}
          </h4>
        )}
        {item.subtitle && (
          <p className={cn("text-ui-text-muted", design?.size === "compact" ? "text-xs" : "text-sm")}>
            {item.subtitle}
          </p>
        )}
      </div>
    </div>
  )
}

export const CollectionWidget = ({ data }: { data: components["schemas"]["CollectionWidget"] }) => {
  const { config } = data
  const { style, itemsData, design, options, pagination } = config

  // In a real implementation, this would fetch data from the configured data source
  // For now, we'll use mock data
  const mockItems = generateMockData(options?.limitItems || 5)

  // Handle empty configuration
  if (!itemsData?.title && !itemsData?.subtitle && !itemsData?.meta && !itemsData?.image) {
    return (
      <div className="px-4 py-2">
        <div className="flex items-center justify-center rounded-lg border-2 border-dashed border-ui-border-base bg-ui-bg-subtle p-8 text-center">
          <div className="text-ui-text-muted">
            <IconDatabase className="mx-auto size-8" />
            <p className="mt-2 text-sm">No collection configuration</p>
          </div>
        </div>
      </div>
    )
  }

  // Handle empty data
  if (!mockItems || mockItems.length === 0) {
    return (
      <div className="px-4 py-2">
        <div className="flex items-center justify-center rounded-lg border border-ui-border-base bg-ui-bg-subtle p-8 text-center">
          <div className="text-ui-text-muted">
            <IconDatabase className="mx-auto size-8" />
            <p className="mt-2 text-sm">No items to display</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="px-4 py-0.5">
      <div
        className={cn(
          collectionVariants({
            style,
            size: design?.size,
          }),
        )}
      >
        {mockItems.map((item) => (
          <CollectionItem key={item.id} item={item} style={style} design={design} itemsData={itemsData} />
        ))}
      </div>

      {pagination?.enabled && (
        <div className="mt-4 flex items-center justify-center">
          <div className="text-ui-text-muted text-xs">
            {pagination.page || "1"} / {pagination.limit || "10"}
          </div>
        </div>
      )}
    </div>
  )
}
