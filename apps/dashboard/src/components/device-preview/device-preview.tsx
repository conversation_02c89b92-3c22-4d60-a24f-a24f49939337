import React from "react"
import { create } from "zustand"

type DevicePreviewProps = {
  children: React.ReactNode
}

type DevicePreviewMode = "mobile" | "tablet"

interface DevicePreviewStore {
  mode: DevicePreviewMode
  setMode: (mode: DevicePreviewMode) => void
}

export const useDevicePreviewStore = create<DevicePreviewStore>((set) => ({
  mode: "mobile",
  setMode: (mode) => set({ mode }),
}))

export const DevicePreview: React.FC<DevicePreviewProps> = ({ children }) => {
  const { mode } = useDevicePreviewStore()
  return (
    <div
      className={`relative overflow-hidden rounded-[2rem] border-8 border-gray-900 transition-all duration-300 dark:border-gray-500 ${
        mode === "mobile" ? "h-[85vh] w-[calc(85vh*9/18)]" : "h-[85vh] w-[calc(85vh*4/5)]"
      }`}
    >
      <div className="h-full w-full overflow-auto">{children}</div>
    </div>
  )
}
