import { createDataTableColumnHelper, DataTableColumnDef, Tooltip } from "@saf/ui"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useDate } from "@/hooks/use-date"

type EntityWithDates = {
  createdAt: string
  updatedAt?: string
}

const columnHelper = createDataTableColumnHelper<EntityWithDates>()

export const useDataTableDateColumns = <TData extends EntityWithDates>() => {
  const { t } = useTranslation()
  const { getFullDate } = useDate()

  return useMemo(() => {
    return [
      columnHelper.accessor("createdAt", {
        header: t("fields.createdAt"),
        cell: ({ row }) => {
          return (
            <Tooltip
              content={getFullDate({
                date: row.original.createdAt,
                includeTime: true,
              })}
            >
              <span>{getFullDate({ date: row.original.createdAt })}</span>
            </Tooltip>
          )
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.dateAsc"),
        sortDescLabel: t("filters.sorting.dateDesc"),
      }),
      columnHelper.accessor("updatedAt", {
        header: t("fields.updatedAt"),
        cell: ({ row }) => {
          return (
            <Tooltip
              content={getFullDate({
                date: row.original.updatedAt || "",
                includeTime: true,
              })}
            >
              <span>{getFullDate({ date: row.original.updatedAt || "" })}</span>
            </Tooltip>
          )
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.dateAsc"),
        sortDescLabel: t("filters.sorting.dateDesc"),
      }),
    ] as DataTableColumnDef<TData>[]
  }, [t, getFullDate])
}
