import { queryClient } from "@/client/react-query"
import { QueryClientProvider } from "@tanstack/react-query"
import { Toaster, TooltipProvider } from "@saf/ui"
import { RouterProvider } from "react-router-dom"
import { I18n } from "./i18n/i18n"
import { ExtensionProvider } from "./providers/extension-provider"
import { I18nProvider } from "./providers/i18n-provider"
import { ThemeProvider } from "./providers/theme-provider"
import { createRouter } from "./router"

export const App = () => {
  const router = createRouter()

  return (
    <TooltipProvider>
      <ExtensionProvider>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider>
            <I18n />
            <I18nProvider>
              <RouterProvider router={router} />
            </I18nProvider>
            <Toaster />
          </ThemeProvider>
        </QueryClientProvider>
      </ExtensionProvider>
    </TooltipProvider>
  )
}
