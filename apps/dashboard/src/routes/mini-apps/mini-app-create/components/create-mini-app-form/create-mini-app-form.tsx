import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON>, <PERSON>ing, Input, Textarea, toast } from "@saf/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"

import { safQuery } from "@/client"
import { Form } from "@/components/common/form"
import { KeyboundForm } from "@/components/common/keybound-form"
import { RouteFocusModal, useRouteModal } from "@/components/modals"
import { optionalEmailSchema } from "@/lib/validations"
import { components } from "@saf/sdk"
import { useQueryClient } from "@tanstack/react-query"
import { useParams } from "react-router-dom"
import { z } from "zod"

export const createMiniAppSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(50, "Name must be less than 50 characters"),
  description: z.string(),
  customerServiceContactNumber: z.string(),
  customerServiceContactEmail: optionalEmailSchema,
  termsAndConditionsUrl: z.union([z.literal(""), z.string().url("Invalid URL format")]),
})

export type CreateMiniAppSchema = z.infer<typeof createMiniAppSchema>

type CreateMiniAppFormProps = {
  miniApp?: components["schemas"]["MiniApp"]
}

export const CreateMiniAppForm = ({ miniApp }: CreateMiniAppFormProps) => {
  const { t } = useTranslation()
  const { handleSuccess } = useRouteModal()
  const queryClient = useQueryClient()
  const { teamId = "" } = useParams()

  const form = useForm<CreateMiniAppSchema>({
    defaultValues: {
      name: miniApp?.name || "",
      description: miniApp?.description || "",
      customerServiceContactNumber: miniApp?.customerServiceContactNumber || "",
      customerServiceContactEmail: miniApp?.customerServiceContactEmail || "",
      termsAndConditionsUrl: miniApp?.termsAndConditionsUrl || "",
    },
    resolver: zodResolver(createMiniAppSchema),
  })

  const { mutate: createMutation, isPending: isCreatePending } = safQuery.useMutation(
    "post",
    "/api/admin/teams/{teamId}/mini-apps",
  )

  const { mutate: editMutation, isPending: isEditPending } = safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}",
  )

  const isEdit = miniApp != null
  const isPending = isCreatePending || isEditPending

  const handleSubmit = form.handleSubmit(async (values) => {
    const invalidateQueries = (miniAppId?: number) => {
      queryClient.invalidateQueries(
        safQuery.queryOptions("get", "/api/admin/teams/{teamId}/mini-apps", {
          params: {
            path: {
              teamId: parseInt(teamId),
            },
          },
        }),
      )
      if (miniAppId) {
        queryClient.invalidateQueries(
          safQuery.queryOptions("get", "/api/admin/teams/{teamId}/mini-apps/{miniAppId}", {
            params: {
              path: {
                teamId: parseInt(teamId),
                miniAppId: miniAppId,
              },
            },
          }),
        )
      }
    }

    if (isEdit) {
      editMutation(
        {
          params: {
            path: {
              teamId: parseInt(teamId),
              miniAppId: miniApp.id,
            },
          },
          body: {
            name: values.name,
            description: values.description,
            customerServiceContactNumber: values.customerServiceContactNumber,
            customerServiceContactEmail: values.customerServiceContactEmail,
            termsAndConditionsUrl: values.termsAndConditionsUrl,
          },
        },
        {
          onSuccess: () => {
            invalidateQueries(miniApp.id)
            toast.success(t("operationFeedback.updateSuccess"))
            handleSuccess()
          },
          onError: (error) => toast.error(error.message),
        },
      )
    } else {
      createMutation(
        {
          params: {
            path: {
              teamId: parseInt(teamId),
            },
          },
          body: {
            name: values.name,
            description: values.description,
            customerServiceContactNumber: values.customerServiceContactNumber,
            customerServiceContactEmail: values.customerServiceContactEmail,
            termsAndConditionsUrl: values.termsAndConditionsUrl,
          },
        },
        {
          onSuccess: (miniApp) => {
            invalidateQueries()
            toast.success(t("operationFeedback.createSuccess"))
            handleSuccess(`../${miniApp.id}`)
          },
          onError: (error) => toast.error(error.message),
        },
      )
    }
  })

  return (
    <RouteFocusModal.Form form={form}>
      <KeyboundForm onSubmit={handleSubmit} className="flex h-full flex-col overflow-hidden">
        <RouteFocusModal.Header />
        <RouteFocusModal.Body className="flex flex-1 flex-col overflow-hidden">
          <div className="flex flex-1 flex-col items-center overflow-y-auto">
            <div className="flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16">
              <Heading className="capitalize">
                {isEdit ? t("miniApps.editMiniApp") : t("miniApps.createMiniApp")}
              </Heading>
              <div className="flex flex-col gap-y-4">
                <Form.Field
                  control={form.control}
                  name="name"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Label>{t("fields.name")}</Form.Label>
                        <Form.Control>
                          <Input size="small" {...field} />
                        </Form.Control>
                        <Form.ErrorMessage />
                      </Form.Item>
                    )
                  }}
                />
                <Form.Field
                  control={form.control}
                  name="description"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Label>{t("fields.description")}</Form.Label>
                        <Form.Control>
                          <Textarea {...field} />
                        </Form.Control>
                        <Form.ErrorMessage />
                      </Form.Item>
                    )
                  }}
                />{" "}
                <Form.Field
                  control={form.control}
                  name="customerServiceContactNumber"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Label>Customer Service Contact Number</Form.Label>
                        <Form.Control>
                          <Input size="small" {...field} />
                        </Form.Control>
                        <Form.ErrorMessage />
                      </Form.Item>
                    )
                  }}
                />
                <Form.Field
                  control={form.control}
                  name="customerServiceContactEmail"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Label>Customer Service Contact Email</Form.Label>
                        <Form.Control>
                          <Input size="small" {...field} />
                        </Form.Control>
                        <Form.ErrorMessage />
                      </Form.Item>
                    )
                  }}
                />
                <Form.Field
                  control={form.control}
                  name="termsAndConditionsUrl"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Label>Terms and Conditions URL</Form.Label>
                        <Form.Control>
                          <Input size="small" {...field} />
                        </Form.Control>
                        <Form.ErrorMessage />
                      </Form.Item>
                    )
                  }}
                />
              </div>
            </div>
          </div>
        </RouteFocusModal.Body>
        <RouteFocusModal.Footer>
          <div className="flex items-center justify-end gap-x-2">
            <RouteFocusModal.Close asChild>
              <Button size="small" variant="secondary">
                {t("actions.cancel")}
              </Button>
            </RouteFocusModal.Close>
            <Button size="small" type="submit" isLoading={isPending}>
              {t("actions.save")}
            </Button>
          </div>
        </RouteFocusModal.Footer>
      </KeyboundForm>
    </RouteFocusModal.Form>
  )
}
