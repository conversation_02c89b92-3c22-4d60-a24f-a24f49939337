import { DataTableEmptyStateProps } from "@saf/ui"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"

export const useMiniAppTableEmptyState = (): DataTableEmptyStateProps => {
  const { t } = useTranslation()

  return useMemo(() => {
    const content: DataTableEmptyStateProps = {
      empty: {
        heading: t("miniApps.list.empty.heading", "No mini apps found"),
        description: t(
          "miniApps.list.empty.description",
          "You haven't created any mini apps yet. Create your first mini app to get started.",
        ),
      },
      filtered: {
        heading: t("miniApps.list.filtered.heading", "No results found"),
        description: t("miniApps.list.filtered.description", "Try adjusting your search or filter parameters."),
      },
    }

    return content
  }, [t])
}
