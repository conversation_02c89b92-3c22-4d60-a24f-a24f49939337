import { useDataTableDateFilters } from "@/components/data-table/helpers/general/use-data-table-date-filters"
import { createDataTableFilterHelper } from "@saf/ui"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"

type MiniApp = {
  id: string
  name: string
  description: string | null
  isPublished: boolean
  createdAt: string
  updatedAt: string
}

const filterHelper = createDataTableFilterHelper<MiniApp>()

export const useMiniAppTableFilters = () => {
  const { t } = useTranslation()
  const dateFilters = useDataTableDateFilters()

  return useMemo(
    () => [
      filterHelper.accessor("isPublished", {
        label: t("fields.status"),
        type: "radio",
        options: [
          {
            label: t("general.published"),
            value: "true",
          },
          {
            label: t("general.draft"),
            value: "false",
          },
        ],
      }),
      ...dateFilters,
    ],
    [dateFilters, t],
  )
}
