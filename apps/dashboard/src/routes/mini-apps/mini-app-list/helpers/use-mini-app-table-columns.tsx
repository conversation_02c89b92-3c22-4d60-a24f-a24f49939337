import { DataTableStatusCell } from "@/components/data-table/components/data-table-status-cell/data-table-status-cell"
import { useDataTableDateColumns } from "@/components/data-table/helpers/general/use-data-table-date-columns"
import { miniAppStatusOption } from "@/configs"
import { reduceOptionsToMap } from "@/lib/utils"
import { components } from "@saf/sdk"
import { createDataTableColumnHelper, Toolt<PERSON> } from "@saf/ui"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"

type MiniApp = components["schemas"]["MiniApp"]

const columnHelper = createDataTableColumnHelper<MiniApp>()

export const useMiniAppTableColumns = () => {
  const { t } = useTranslation()
  const dateColumns = useDataTableDateColumns<MiniApp>()

  const miniAppStatusMap = reduceOptionsToMap(miniAppStatusOption)

  return useMemo(
    () => [
      columnHelper.accessor("name", {
        header: () => t("fields.name"),
        enableSorting: true,
        sortLabel: t("fields.name"),
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc"),
      }),
      columnHelper.accessor("description", {
        header: () => t("fields.description"),
        cell: ({ getValue }) => {
          const value = getValue()
          return (
            <Tooltip content={value || ""}>
              <div className="flex h-full w-full items-center overflow-hidden">
                <span className="truncate">{value || "-"}</span>
              </div>
            </Tooltip>
          )
        },
        maxSize: 250,
        minSize: 100,
      }),
      columnHelper.accessor("miniAppStatus", {
        header: () => t("fields.status"),
        cell: ({ getValue }) => {
          const value = getValue()
          return (
            <DataTableStatusCell color={value == "removed_by_admin" ? "red" : "green"}>
              {miniAppStatusMap[value].label}
            </DataTableStatusCell>
          )
        },
      }),
      ...dateColumns,
    ],
    [t, dateColumns, miniAppStatusMap],
  )
}
