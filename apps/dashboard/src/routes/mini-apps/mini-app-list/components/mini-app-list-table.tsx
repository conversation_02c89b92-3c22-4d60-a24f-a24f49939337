import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { <PERSON>rrorR<PERSON>ult } from "@/components/common/error-result"
import { usePaginationQueryParam } from "@/hooks/use-pagination-query-params"
import { PencilSquare, Trash } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { Container, createDataTableColumnHelper, toast, usePrompt } from "@saf/ui"
import { keepPreviousData } from "@tanstack/react-query"
import { useCallback, useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate, useParams } from "react-router-dom"
import { DataTable } from "../../../../components/data-table"
import * as hooks from "../helpers"

const PAGE_SIZE = 16

export const MiniAppListTable = () => {
  const { t } = useTranslation()
  const { teamId = "" } = useParams()

  const queryParams = usePaginationQueryParam([])

  const { data, isLoading, error } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
        },
        query: {
          ...queryParams,
        },
      },
    },
    {
      placeholderData: keepPreviousData,
    },
  )

  const columns = useColumns()
  const filters = hooks.useMiniAppTableFilters()
  const emptyState = hooks.useMiniAppTableEmptyState()

  const defaultValue = useMemo(() => [], [])

  if (error) {
    return <ErrorResult message={error.message} />
  }

  return (
    <Container className="p-0">
      <DataTable
        data={data?.items || defaultValue}
        columns={columns}
        rowCount={data?.totalCount || 0}
        getRowId={(row) => `${row.id}`}
        pageSize={PAGE_SIZE}
        filters={filters}
        isLoading={isLoading}
        emptyState={emptyState}
        heading={t("miniApps.domain")}
        action={{
          label: t("actions.create"),
          to: "create",
        }}
        rowHref={(row) => `${row.id}`}
      />
    </Container>
  )
}

type MiniApp = components["schemas"]["MiniApp"]

const columnHelper = createDataTableColumnHelper<MiniApp>()

export const useDeleteMiniAppPrompt = () => {
  const { t } = useTranslation()
  const prompt = usePrompt()

  const { mutate } = safQuery.useMutation("delete", "/api/admin/teams/{teamId}/mini-apps/{miniAppId}")

  const { teamId = "" } = useParams()

  const handleDelete = useCallback(
    async ({ miniApp, onSuccess }: { miniApp: MiniApp; onSuccess?: (miniApp: MiniApp) => void }) => {
      const confirm = await prompt({
        title: t("general.areYouSure"),
        description: t("miniApps.deleteWarning", {
          title: miniApp.name,
        }),
        verificationInstruction: t("general.typeToConfirm"),
        verificationText: miniApp.name,
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel"),
      })

      if (!confirm) {
        return
      }

      mutate(
        {
          params: {
            path: {
              teamId: parseInt(teamId || ""),
              miniAppId: miniApp.id,
            },
          },
        },
        {
          onSuccess: () => {
            toast.success(t("operationFeedback.deleteSuccess"))
            queryClient.invalidateQueries(
              safQuery.queryOptions("get", "/api/admin/teams/{teamId}/mini-apps", {
                params: {
                  path: {
                    teamId: parseInt(teamId || ""),
                  },
                },
              }),
            )
            onSuccess?.(miniApp)
          },
          onError: (error) => {
            toast.error(error.message)
          },
        },
      )
    },
    [prompt, t, mutate, teamId],
  )

  return handleDelete
}

const useColumns = () => {
  const navigate = useNavigate()
  const base = hooks.useMiniAppTableColumns()

  const handleDelete = useDeleteMiniAppPrompt()

  return useMemo(
    () => [
      ...base,
      columnHelper.action({
        actions: (ctx) => {
          return [
            [
              {
                icon: <PencilSquare />,
                onClick: () => navigate(`${ctx.row.original.id}/edit`),
              },
            ],
            [
              {
                icon: <Trash />,
                onClick: () =>
                  handleDelete({
                    miniApp: ctx.row.original,
                  }),
              },
            ],
          ]
        },
      }),
    ],
    [base, handleDelete, navigate],
  )
}
