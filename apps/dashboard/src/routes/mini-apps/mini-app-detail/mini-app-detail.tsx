import { safQuery } from "@/client"
import { SectionRow } from "@/components/common/section"
import { miniAppStatusOption } from "@/configs"
import { useDate } from "@/hooks/use-date"
import { reduceOptionsToMap } from "@/lib/utils"
import { <PERSON><PERSON>, Container, Heading, StatusBadge, Text } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { Link, useNavigate, useParams } from "react-router-dom"
import { SingleColumnPageSkeleton } from "../../../components/common/skeleton"
import { SingleColumnPage } from "../../../components/layout/pages"
import { useDeleteMiniAppPrompt } from "../mini-app-list/components"
import { MiniAppVersionList } from "./components"

export const MiniAppDetail = () => {
  const { t } = useTranslation()
  const { getFullDate } = useDate()
  const navigate = useNavigate()
  const { teamId = "", miniAppId = "" } = useParams()

  const {
    data: miniApp,
    isLoading,
    isError,
    error,
  } = safQuery.useQuery("get", `/api/admin/teams/{teamId}/mini-apps/{miniAppId}`, {
    params: {
      path: {
        teamId: parseInt(teamId),
        miniAppId: parseInt(miniAppId),
      },
    },
  })

  const handleDelete = useDeleteMiniAppPrompt()

  if (isLoading || !miniApp) {
    return <SingleColumnPageSkeleton />
  }

  if (isError) {
    throw error
  }

  const miniAppStatusMap = reduceOptionsToMap(miniAppStatusOption)

  return (
    <SingleColumnPage data={miniApp}>
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between gap-4 px-6 py-4">
          <div className="space-y-1">
            <Heading className="flex items-center gap-x-2">
              {miniApp.name}
              <StatusBadge color={miniApp.miniAppStatus == "removed_by_admin" ? "red" : "green"}>
                {miniAppStatusMap[miniApp.miniAppStatus].label}
              </StatusBadge>
            </Heading>
            <Text size="small">{miniApp.description || "No description"}</Text>
          </div>
          <div className="flex shrink-0 items-center gap-x-2">
            <Button size="small" variant="secondary" asChild>
              <Link to="edit">{t("edit", "Edit")}</Link>
            </Button>
            <Button
              size="small"
              variant="danger"
              onClick={() =>
                handleDelete({
                  miniApp,
                  onSuccess: () => {
                    navigate("..")
                  },
                })
              }
            >
              {t("delete", "Delete")}
            </Button>
          </div>
        </div>
        <SectionRow title="Support Phone No." value={miniApp.customerServiceContactNumber || "-"} />
        <SectionRow title="Support Email" value={miniApp.customerServiceContactEmail || "-"} />
        <SectionRow
          title="Terms and Conditions"
          value={
            miniApp.termsAndConditionsUrl ? (
              <Link to={miniApp.termsAndConditionsUrl} target="_blank" rel="noopener noreferrer">
                <Text size="small" className="underline">
                  {miniApp.termsAndConditionsUrl}
                </Text>
              </Link>
            ) : (
              "-"
            )
          }
        />
        <SectionRow
          title="Created At"
          value={`${getFullDate({ date: miniApp.createdAt })} by ${miniApp.createdBy || "-"}`}
        />
        {miniApp.updatedAt && (
          <SectionRow
            title="Updated At"
            value={`${getFullDate({ date: miniApp.updatedAt })} by ${miniApp.updatedBy || "-"}`}
          />
        )}
      </Container>
      <MiniAppVersionList />
    </SingleColumnPage>
  )
}
