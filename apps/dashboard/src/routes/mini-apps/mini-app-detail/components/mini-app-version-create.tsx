import { Form } from "@/components/common/form"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON>, Drawer, Input, RadioGroup, Textarea, toast } from "@saf/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"

import { safQuery } from "@/client"
import { Field } from "@/components/common/field"
import { miniAppTypeOptions } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { components } from "@saf/sdk"
import { useQueryClient } from "@tanstack/react-query"
import { useParams } from "react-router-dom"
import { z } from "zod"

export const createMiniAppVersionSchema = z.object({
  version: z.string().min(2, "Name must be at least 2 characters").max(50, "Name must be less than 50 characters"),
  miniAppType: z.enum(createEnumFromOptions(miniAppTypeOptions)),
  releaseNote: z.string(),
  miniAppUrl: z.string(),
  thumbnailUrl: z.string(),
  url: z.string(),
})

export const CreateMiniAppVersionForm = ({
  open,
  onOpenChange,
  onSuccess,
  miniAppVersion,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
  miniAppVersion?: components["schemas"]["MiniAppVersion"]
}) => {
  const { t } = useTranslation()
  const queryClient = useQueryClient()
  const { teamId = "", miniAppId = "" } = useParams()

  const form = useForm<z.infer<typeof createMiniAppVersionSchema>>({
    defaultValues: {
      version: miniAppVersion?.version || "",
      miniAppType: miniAppVersion?.miniAppType || miniAppTypeOptions[0].value,
      releaseNote: miniAppVersion?.releaseNote || "",
      miniAppUrl: miniAppVersion?.miniAppUrl || "",
      thumbnailUrl: miniAppVersion?.thumbnailUrl || "",
      url: miniAppVersion?.url || "",
    },
    resolver: zodResolver(createMiniAppVersionSchema),
  })

  const { mutateAsync: createMutation, isPending: isCreatePending } = safQuery.useMutation(
    "post",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions",
  )

  const { mutateAsync: editMutation, isPending: isEditPending } = safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}",
  )

  const isEdit = miniAppVersion != null
  const isPending = isCreatePending || isEditPending

  const handleSubmit = form.handleSubmit(async (values) => {
    const invalidateQueries = (miniAppVersionId?: number) => {
      queryClient.invalidateQueries(
        safQuery.queryOptions("get", "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions", {
          params: {
            path: {
              teamId: parseInt(teamId),
              miniAppId: parseInt(miniAppId),
            },
          },
        }),
      )
      if (miniAppVersionId) {
        queryClient.invalidateQueries(
          safQuery.queryOptions(
            "get",
            "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}",
            {
              params: {
                path: {
                  teamId: parseInt(teamId),
                  miniAppId: parseInt(miniAppId),
                  versionId: miniAppVersionId,
                },
              },
            },
          ),
        )
      }
    }

    try {
      if (isEdit) {
        await editMutation({
          params: {
            path: {
              teamId: parseInt(teamId),
              miniAppId: parseInt(miniAppId),
              versionId: miniAppVersion.id,
            },
          },
          body: {
            version: values.version,
            releaseNote: values.releaseNote,
            miniAppType: values.miniAppType,
            miniAppUrl: values.miniAppUrl || undefined,
            thumbnailUrl: values.thumbnailUrl || undefined,
            url: values.url || undefined,
          },
        })
      } else {
        await createMutation({
          params: {
            path: {
              teamId: parseInt(teamId),
              miniAppId: parseInt(miniAppId),
            },
          },
          body: {
            version: values.version,
            releaseNote: values.releaseNote,
            miniAppType: values.miniAppType,
            miniAppUrl: values.miniAppUrl || undefined,
            thumbnailUrl: values.thumbnailUrl || undefined,
            url: values.url || undefined,
          },
        })
      }
      invalidateQueries(miniAppVersion?.id)
      toast.success(t("operationFeedback.createSuccess"))
      form.reset()
      onSuccess?.()
    } catch (error) {
      if (miniAppVersion != null) {
        toast.success(t("operationFeedback.updateFailure"))
      } else {
        toast.success(t("operationFeedback.createFailure"))
      }

      toast.error(showHumanFriendlyError(error))
    }
  })

  form.watch("miniAppType")

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <Drawer.Content>
        <Drawer.Header>{isEdit ? t("miniAppsVersion.editTitle") : t("miniAppsVersion.createTitle")}</Drawer.Header>
        <Form {...form}>
          <form onSubmit={handleSubmit} className="flex h-full flex-col overflow-hidden">
            <Drawer.Body className="flex flex-1 flex-col overflow-y-auto">
              <div className="flex flex-col gap-y-4">
                <Field {...form} name="version" label="Version">
                  <Input size="small" />
                </Field>
                <Field {...form} name="releaseNote" label="Release Note">
                  <Textarea />
                </Field>
                <Field {...form} name="thumbnailUrl" label="App Icon">
                  <Input type="file" accept="image/*" />
                </Field>
                <Form.Field
                  control={form.control}
                  name="miniAppType"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Label>Type</Form.Label>
                        <Form.Control>
                          <RadioGroup
                            className="flex flex-col gap-y-3 md:flex-row [&>*]:flex-1"
                            {...field}
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            {miniAppTypeOptions.map((option) => (
                              <RadioGroup.ChoiceBox key={option.value} value={option.value} label={option.label} />
                            ))}
                          </RadioGroup>
                        </Form.Control>
                        <Form.ErrorMessage />
                      </Form.Item>
                    )
                  }}
                />
                {
                  {
                    bundle: (
                      <Field {...form} name="miniAppUrl" label="Mini App Bundle">
                        <Input type="file" accept="application/zip,application/x-zip-compressed" />
                      </Field>
                    ),
                    url: (
                      <>
                        <Field {...form} name="miniAppUrl" label="Mini App URL">
                          <Input size="small" />
                        </Field>
                      </>
                    ),
                    builder: null,
                    undefined: null,
                  }[form.getValues("miniAppType")]
                }
              </div>
            </Drawer.Body>
            <Drawer.Footer>
              <div className="flex items-center justify-end gap-x-2">
                <Drawer.Close asChild>
                  <Button size="small" variant="secondary">
                    {t("actions.cancel")}
                  </Button>
                </Drawer.Close>
                <Button size="small" type="submit" isLoading={isPending}>
                  {t("actions.save")}
                </Button>
              </div>
            </Drawer.Footer>
          </form>
        </Form>
      </Drawer.Content>
    </Drawer>
  )
}
