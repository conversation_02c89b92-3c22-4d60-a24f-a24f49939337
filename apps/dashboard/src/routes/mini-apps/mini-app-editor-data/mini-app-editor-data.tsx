import { Divider, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>on, Popover } from "@saf/ui"
import { IconPlus } from "@tabler/icons-react"
import React from "react"
import { Outlet } from "react-router-dom"
import { DataSourceCreateForm } from "./data-sources/components/data-source-create-form"
import { DataSourceList } from "./data-sources/components/data-source-list"

export const MiniAppEditorData = () => {
  const [open, setOpen] = React.useState(false)

  return (
    <div className="flex h-full divide-x overflow-hidden">
      <div className="flex h-full min-w-[230px] flex-col overflow-hidden">
        <div className="flex h-[var(--topbar-height)] items-center justify-between px-4 py-2">
          <Heading level="h3">Data Sources</Heading>
          <Popover open={open} onOpenChange={setOpen}>
            <Popover.Trigger asChild>
              <IconButton size="xsmall" variant="primary">
                <IconPlus className="size-4" />
              </IconButton>
            </Popover.Trigger>
            <Popover.Content className="w-80 p-0" side="right">
              <DataSourceCreateForm
                onSuccess={() => {
                  setOpen(false)
                }}
              />
            </Popover.Content>
          </Popover>
        </div>
        <Divider orientation="horizontal" />
        <div className="flex-1 overflow-y-auto p-2">
          <DataSourceList />
        </div>
      </div>
      <div className="h-full flex-1">
        <Outlet />
      </div>
    </div>
  )
}
