import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { ErrorR<PERSON>ult } from "@/components/common/error-result"
import { TextSkeleton } from "@/components/common/skeleton"
import { cn } from "@/lib/utils"
import { Trash } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { Badge, Divider, Heading, IconButton, Text, toast, usePrompt } from "@saf/ui"
import { IconPlus } from "@tabler/icons-react"
import { keepPreviousData } from "@tanstack/react-query"
import { useCallback } from "react"
import { useTranslation } from "react-i18next"
import { Link, Outlet, useNavigate, useParams } from "react-router-dom"
import { getHttpMethodBadgeColor } from "../utils"
import { ExternalApiCreateForm } from "./components/external-api-create-form"

export const ExternalApis = ({ data }: { data: components["schemas"]["ExternalApiDataSource"] }) => {
  return (
    <>
      <div className="flex h-full divide-x">
        <div className="flex-1">
          <ExternalApiCreateForm data={data} />
        </div>
        <div className="flex-1">
          <div className="flex h-full min-w-[270px] flex-col overflow-hidden">
            <div className="flex h-[var(--topbar-height)] items-center justify-between px-4 py-2">
              <Heading level="h3">API Endpoints</Heading>
              <IconButton size="xsmall" variant="primary" asChild>
                <Link to="external-apis/create">
                  <IconPlus className="size-4" />
                </Link>
              </IconButton>
            </div>
            <Divider orientation="horizontal" />
            <div className="flex-1 overflow-y-auto p-4">
              <ApiEndpointList />
            </div>
          </div>
        </div>
      </div>
      <Outlet />
    </>
  )
}

export const ApiEndpointList = () => {
  const { t } = useTranslation()
  const { teamId = "", miniAppId = "", dataSourceId = "", externalApiId } = useParams()

  const navigate = useNavigate()

  const { data, isLoading, error } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
          dataSourceId: parseInt(dataSourceId),
        },
        query: {
          page: "1",
          limit: "200",
        },
      },
    },
    {
      placeholderData: keepPreviousData,
    },
  )
  const { handleDelete, isPending } = useDeletePrompt()

  if (error) {
    return <ErrorResult message={error.message} />
  }

  return (
    <>
      {isLoading && (
        <div className="flex flex-col gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <TextSkeleton key={index} characters={20} />
          ))}
        </div>
      )}
      <div className="flex flex-col gap-2">
        {data?.items.map((item) => (
          <div
            key={item.id}
            className={cn(
              "group flex cursor-pointer items-center justify-between overflow-hidden rounded-md border border-ui-border-base bg-ui-bg-subtle-hover py-2 pe-1 ps-2 transition-colors hover:bg-ui-bg-subtle-hover",
              externalApiId != null && `${item.id}` === externalApiId
                ? "bg-ui-bg-base shadow-sm hover:bg-ui-bg-base"
                : "",
            )}
            aria-disabled={isPending}
            onClick={() => navigate(`external-apis/${item.id}`)}
          >
            <div className="flex shrink-0 items-center gap-2">
              <Badge
                color={getHttpMethodBadgeColor(item.httpMethod)}
                size="xsmall"
                className="min-w-16 items-center text-center uppercase"
              >
                {item.httpMethod}
              </Badge>
              <Text weight="plus" className="min-w-0 truncate">
                {item.urlPath}
              </Text>
              <Text size="xsmall" weight="regular" className="min-w-0 truncate">
                {item.name}
              </Text>
            </div>
            <IconButton
              variant="transparent"
              size="xsmall"
              className="text-ui-fg-error opacity-0 transition-opacity group-hover:opacity-100"
              onClick={(e) => {
                e.stopPropagation()
                handleDelete({
                  teamId: parseInt(teamId),
                  miniAppId: parseInt(miniAppId),
                  dataSourceId: parseInt(dataSourceId || ""),
                  externalApiId: item.id,
                  onSuccess: () => {
                    if (externalApiId === `${item.id}`) {
                      navigate("./..")
                    }
                  },
                })
              }}
              isLoading={isPending}
            >
              <Trash />
            </IconButton>
          </div>
        ))}
      </div>
      {data?.items.length === 0 && <Text>{t("emptyState.list.empty.description")}</Text>}
    </>
  )
}

export const useDeletePrompt = () => {
  const { t } = useTranslation()
  const prompt = usePrompt()

  const { mutate, isPending } = safQuery.useMutation(
    "delete",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}",
  )

  const handleDelete = useCallback(
    async ({
      teamId,
      miniAppId,
      dataSourceId,
      externalApiId,
      onSuccess,
    }: {
      teamId: number
      miniAppId: number
      dataSourceId: number
      externalApiId: number
      onSuccess?: () => void
    }) => {
      const confirm = await prompt({
        title: t("general.areYouSure"),
        description: t("general.areYouSureDescription", {
          entity: "Endpoint",
        }),
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel"),
      })

      if (!confirm) {
        return
      }

      mutate(
        {
          params: {
            path: {
              teamId,
              miniAppId,
              dataSourceId,
              externalApiId,
            },
          },
        },
        {
          onSuccess: () => {
            toast.success(t("operationFeedback.deleteSuccess"))
            queryClient.invalidateQueries(
              safQuery.queryOptions(
                "get",
                "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis",
                {
                  params: {
                    path: {
                      teamId,
                      miniAppId,
                      dataSourceId,
                    },
                  },
                },
              ),
            )
            onSuccess?.()
          },
          onError: (error) => {
            toast.error(error.message)
          },
        },
      )
    },
    [prompt, t, mutate],
  )

  return { handleDelete, isPending }
}
