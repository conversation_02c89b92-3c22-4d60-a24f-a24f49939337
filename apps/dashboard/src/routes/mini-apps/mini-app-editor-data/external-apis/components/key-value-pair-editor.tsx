import { Field } from "@/components/common/field"
import { Plus, Trash } from "@medusajs/icons"
import { Button, IconButton, Input, Text } from "@saf/ui"
import { ArrayPath, Control, FieldArray, FieldValues, useFieldArray } from "react-hook-form"
import { z } from "zod"

export const defaultKeyValue: {
  key: string
  value: string
} = { key: "", value: "" }

export const keyValueArraySchema = z.array(
  z.object({
    key: z.string(),
    value: z.string(),
  }),
)

/**
 * More type-safe version of KeyValuePairEditor that works with react-hook-form
 * TFieldValues: The type of the form values
 * TName: The name of the field array in the form
 */
interface KeyValuePairEditorProps<
  TFieldValues extends FieldValues = FieldValues,
  // TName extends ArrayPath<TFieldValues> = ArrayPath<TFieldValues>,
> {
  /**
   * The field array from useFieldArray
   */
  name: ArrayPath<TFieldValues>
  /**
   * The form control from useForm
   */
  control: Control<TFieldValues>
  /**
   * Placeholder text for key inputs
   */
  keyPlaceholder?: string
  /**
   * Placeholder text for value inputs
   */
  valuePlaceholder?: string
  /**
   * Text for the add button
   */
  addButtonText?: string
  /**
   * Message to show when no items exist
   */
  emptyMessage?: string
  /**
   * Optional default value for new items
   */
  defaultValue?: FieldArray<TFieldValues, ArrayPath<TFieldValues>> | FieldArray<TFieldValues, ArrayPath<TFieldValues>>[]
  /**
   * Optional flag to show label for key and value inputs
   */
  showLabel?: boolean
  disableKeyInput?: boolean
  disableAddButton?: boolean
  onRemove?: (index: number) => void
}

export const KeyValuePairEditor = <
  TFieldValues extends FieldValues = FieldValues,
  // TName extends ArrayPath<TFieldValues> = ArrayPath<TFieldValues>,
>({
  name,
  control,
  keyPlaceholder = "Key",
  valuePlaceholder = "Value",
  showLabel = true,
  addButtonText = "Add More",
  emptyMessage = 'No value added. Click "Add More" to add one.',
  defaultValue = { id: "", key: "", value: "" } as any,
  disableKeyInput = false,
  disableAddButton = false,
  onRemove,
}: KeyValuePairEditorProps<TFieldValues>) => {
  const fieldArray = useFieldArray({
    control: control,
    name: name,
  })

  const { fields, append, remove } = fieldArray

  return (
    <div className="flex flex-col gap-3">
      {fields.length === 0 ? (
        <Text className="text-ui-fg-subtle">{emptyMessage}</Text>
      ) : (
        fields.map((field, index) => (
          <div key={field.id} className="flex items-end gap-2">
            <div className="flex-1">
              {index === 0 && showLabel && (
                <Text size="small" weight="plus" className="mb-2">
                  Key
                </Text>
              )}
              <Field name={`${name}.${index}.key` as any} control={control}>
                <Input variant="pop" placeholder={keyPlaceholder} disabled={disableKeyInput} />
              </Field>
            </div>
            <div className="flex-1">
              {index === 0 && showLabel && (
                <Text size="small" weight="plus" className="mb-2">
                  Value
                </Text>
              )}
              <Field name={`${name}.${index}.value` as any} control={control}>
                <Input variant="pop" placeholder={valuePlaceholder} />
              </Field>
            </div>
            <IconButton
              type="button"
              variant="transparent"
              onClick={() => {
                // Call onRemove first in case need to access the array item before it is removed
                if (typeof onRemove === "function") {
                  onRemove(index)
                }
                remove(index)
              }}
            >
              <Trash className="text-ui-fg-subtle" />
            </IconButton>
          </div>
        ))
      )}
      {!disableAddButton && (
        <Button variant="transparent" size="small" type="button" onClick={() => append(defaultValue)}>
          <Plus />
          {addButtonText}
        </Button>
      )}
    </div>
  )
}
