import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { convertRecordValuesToString, keyValueArrayToRecord, parseJSONValues, recordToKeyValueArray } from "@/lib/utils"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Alert, Button, Input, Text, toast } from "@saf/ui"
import { useCallback, useEffect } from "react"
import { useForm, useFormContext } from "react-hook-form"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { z } from "zod"
import { usePathParameterDetection } from "../hooks/use-path-parameter-detection"
import { defaultKeyValue, keyValueArraySchema, KeyValuePairEditor } from "./key-value-pair-editor"

const externalApiCreateForm = z.object({
  name: z.string().optional(),
  baseUrl: z.string().min(1, { message: "Base URL is required" }),
  headers: keyValueArraySchema.optional(),
  routeParameters: keyValueArraySchema.optional(),
  queryParameters: keyValueArraySchema.optional(),
  body: keyValueArraySchema.optional(),
  cookies: keyValueArraySchema.optional(),
})

type GlobalApiResponse = components["schemas"]["ExternalApiDataSource"]

export const ExternalApiCreateForm = ({ data }: { data: GlobalApiResponse }) => {
  const { teamId = "", miniAppId = "" } = useParams()
  const { t } = useTranslation()

  const getFormDataWithDefaultValue = useCallback((newData: GlobalApiResponse) => {
    const config = newData.config
    return {
      name: newData.name || "",
      baseUrl: config?.baseUrl || "",
      headers: [...recordToKeyValueArray(config?.headers), defaultKeyValue],
      routeParameters: [...recordToKeyValueArray(config?.routeParameters)],
      queryParameters: [...recordToKeyValueArray(config?.queryParameters), defaultKeyValue],
      body: [...recordToKeyValueArray(convertRecordValuesToString(config?.body || {})), defaultKeyValue],
      cookies: [...recordToKeyValueArray(config?.cookies), defaultKeyValue],
    }
  }, [])

  const form = useForm<z.infer<typeof externalApiCreateForm>>({
    resolver: zodResolver(externalApiCreateForm),
    defaultValues: getFormDataWithDefaultValue(data),
  })

  useEffect(() => {
    if (data) {
      form.reset(getFormDataWithDefaultValue(data))
    }
  }, [form, data, getFormDataWithDefaultValue])

  const { mutate, isPending } = safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}",
  )

  const handleSubmit = form.handleSubmit(async (values) => {
    mutate(
      {
        params: {
          path: {
            teamId: parseInt(teamId),
            miniAppId: parseInt(miniAppId),
            dataSourceId: data.id,
          },
        },
        body: {
          name: values.name,
          config: {
            baseUrl: values.baseUrl,
            headers: keyValueArrayToRecord(values.headers),
            routeParameters: keyValueArrayToRecord(values.routeParameters),
            queryParameters: keyValueArrayToRecord(values.queryParameters),
            body: parseJSONValues(keyValueArrayToRecord(values.body) ?? {}),
            cookies: keyValueArrayToRecord(values.cookies),
          },
        },
      },
      {
        onSuccess: () => {
          toast.success(t("operationFeedback.updateSuccess"))
          queryClient.invalidateQueries(
            safQuery.queryOptions("get", "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources", {
              params: {
                path: {
                  teamId: parseInt(teamId),
                  miniAppId: parseInt(miniAppId),
                },
              },
            }),
          )
        },
        onError: (error) => {
          toast.error(showHumanFriendlyError(error))
        },
      },
    )
  })
  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="flex h-full flex-col divide-y">
        {form.formState.errors.root && (
          <Alert variant="error" dismissible={false} className="text-balance">
            {form.formState.errors.root.message}
          </Alert>
        )}
        <div className="h-[var(--topbar-height)] space-y-2 px-4 py-2">
          <div className="flex gap-2">
            <Field {...form} name="name" className="flex-1">
              <Input
                className="border-none bg-ui-bg-field text-xl font-medium shadow-none placeholder:text-lg placeholder:font-normal"
                placeholder="External API Name"
              />
            </Field>
            <Button type="submit" isLoading={isPending}>
              Save
            </Button>
          </div>
        </div>
        <div className="flex-1 divide-y overflow-y-auto">
          <div className="space-y-3 p-4">
            <Field {...form} name="baseUrl" label="Base URL" description="Example: https://api.example.com/{{version}}">
              <Input variant="pop" placeholder="https://api.example.com" className="w-full max-w-none" />
            </Field>
          </div>
          <div className="space-y-3 p-4">
            <Text size="small" weight="plus">
              Header
            </Text>
            <KeyValuePairEditor
              name="headers"
              control={form.control}
              showLabel={false}
              emptyMessage="No headers added. Click 'Add More' to add one."
              defaultValue={defaultKeyValue}
            />
          </div>

          <PathParameterValueEditor />

          <div className="space-y-3 p-4">
            <Text size="small" weight="plus">
              Query Parameters
            </Text>
            <KeyValuePairEditor
              name="queryParameters"
              control={form.control}
              showLabel={false}
              emptyMessage="No query parameters added. Click 'Add More' to add one."
              defaultValue={defaultKeyValue}
            />
          </div>

          <div className="space-y-3 p-4">
            <Text size="small" weight="plus">
              Body
            </Text>
            <KeyValuePairEditor
              name="body"
              control={form.control}
              showLabel={false}
              emptyMessage="No body fields added. Click 'Add More' to add one."
              defaultValue={defaultKeyValue}
            />
          </div>

          <div className="space-y-3 p-4">
            <Text size="small" weight="plus">
              Cookies
            </Text>
            <KeyValuePairEditor
              name="cookies"
              control={form.control}
              showLabel={false}
              emptyMessage="No cookies added. Click 'Add More' to add one."
              defaultValue={defaultKeyValue}
            />
          </div>
        </div>
      </form>
    </Form>
  )
}

export const removePathParam = (urlOrPath: string, index: number) => {
  return urlOrPath.replace(/\/?\{\{.*?\}\}/g, (match, offset) => {
    const tokenIndex = [...urlOrPath.matchAll(/\/?\{\{.*?\}\}/g)].findIndex((m) => m.index === offset)
    return tokenIndex === index ? "" : match
  })
}

const PathParameterValueEditor = () => {
  const form = useFormContext<z.infer<typeof externalApiCreateForm>>()

  usePathParameterDetection({ form })

  const routeParameters = form.watch("routeParameters")
  const hasrouteParameters = routeParameters && routeParameters.length > 0

  const removerouteParametersFromUrl = (index: number) => {
    const baseUrl = form.getValues("baseUrl")
    const updatedBaseUrl = removePathParam(baseUrl, index)
    form.setValue("baseUrl", updatedBaseUrl)
  }

  return (
    hasrouteParameters && (
      <div className="space-y-3 p-4">
        <Text size="small" weight="plus">
          Path Parameters
        </Text>
        <KeyValuePairEditor
          name="routeParameters"
          control={form.control}
          showLabel={false}
          disableKeyInput
          disableAddButton
          onRemove={(index) => {
            removerouteParametersFromUrl(index)
          }}
        />
      </div>
    )
  )
}
