import { useEffect } from "react"
import { FieldValues, Path, UseFormReturn } from "react-hook-form"

// Default parameter type
interface DefaultPathParam {
  key: string
  value: string
}

/**
 * Hook to automatically detect and sync path parameters from a URL field
 * @template TFormValues - Type of the form values object
 * @template TParam - Type of path parameter object (must extend { key: string })
 * @param {Object} options - Configuration options
 * @param {UseFormReturn<TFormValues>} options.form - React Hook Form instance
 * @param {Function} options.subscribe - Form subscription function from react-hook-form
 * @param {Path<TFormValues>} options.urlFieldName - Name of the form field containing the URL (default: 'baseUrl')
 * @param {Path<TFormValues>} options.paramsFieldName - Name of the form field for path parameters (default: 'routeParameters')
 * @param {RegExp} options.paramRegex - Regex to match path parameters (default: /\{\{([^}]+)\}\}/g)
 * @param {(paramName: string, existingParam?: TParam) => TParam} options.createParam - Function to create new parameter objects
 */
export const usePathParameterDetection = <
  TFormValues extends FieldValues = FieldValues,
  TParam extends { key: string } = DefaultPathParam,
>({
  form,
  urlFieldName = "baseUrl" as Path<TFormValues>,
  paramsFieldName = "routeParameters" as Path<TFormValues>,
  paramRegex = /\{\{([^}]+)\}\}/g,
  createParam,
}: {
  form: UseFormReturn<TFormValues>
  urlFieldName?: Path<TFormValues>
  paramsFieldName?: Path<TFormValues>
  paramRegex?: RegExp
  createParam?: (paramName: string, existingParam?: TParam) => TParam
}) => {
  const { subscribe, setValue } = form
  useEffect(() => {
    const callback = subscribe({
      name: urlFieldName,
      formState: {
        values: true,
      },
      callback: ({ values }: { values: TFormValues }) => {
        const url = (values[urlFieldName] as string) || ""

        const currentPathParams: TParam[] = (values[paramsFieldName] as TParam[]) || []

        // Extract path parameters from URL using the provided regex
        const pathParamMatches = url.match(paramRegex) || []
        const detectedParams = pathParamMatches.map(
          (match) => match.replace(/[{}]/g, ""), // Remove {{ }} to get just the parameter name
        )

        // Create a map of existing parameters by key for lookup
        const existingParamsMap = new Map(currentPathParams.map((param) => [param.key, param]))

        // Build new path parameters array based on detected parameters order
        const newPathParams: TParam[] = detectedParams.map((paramName) => {
          const existingParam = existingParamsMap.get(paramName)

          if (createParam) {
            return createParam(paramName, existingParam)
          }

          // Default behavior for DefaultPathParam type
          return {
            key: paramName,
            value: (existingParam as any)?.value || "",
          } as unknown as TParam
        })

        // Only update if there are actual changes to prevent unnecessary re-renders
        const hasChanges =
          newPathParams.length !== currentPathParams.length ||
          newPathParams.some((param, index) => {
            const current = currentPathParams[index]
            return !current || param.key !== current.key
          })

        if (hasChanges) {
          // Use setTimeout to ensure this runs after the current render cycle
          setTimeout(() => {
            setValue(paramsFieldName, newPathParams as any)
          }, 0)
        }
      },
    })

    return () => callback()
  }, [subscribe, setValue, urlFieldName, paramsFieldName, paramRegex, createParam])
}
