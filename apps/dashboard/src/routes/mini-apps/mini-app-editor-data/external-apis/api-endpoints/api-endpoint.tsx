import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { <PERSON>rro<PERSON><PERSON><PERSON><PERSON> } from "@/components/common/error-result"
import { TextSkeleton } from "@/components/common/skeleton"
import { RouteFocusModal } from "@/components/modals"
import { cn } from "@/lib/utils"
import { Trash } from "@medusajs/icons"
import { Divider, Heading, IconButton, Text, toast, usePrompt } from "@saf/ui"
import { IconPlus } from "@tabler/icons-react"
import { keepPreviousData } from "@tanstack/react-query"
import { useCallback } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate, useParams } from "react-router-dom"
import { ApiEndpointCreateForm } from "./components/api-endpoint-create-form"

export const ApiEndpoint = () => {
  const { t } = useTranslation()
  const { teamId = "", miniAppId = "", dataSourceId = "", versionId = "", externalApiId } = useParams()
  const navigate = useNavigate()

  const navigateExternalApis = (path: string) => {
    navigate(
      `/teams/${teamId}/mini-apps/${miniAppId}/versions/${versionId}/data-sources/${dataSourceId}/external-apis/${path}`,
    )
  }

  const { data, isLoading, error } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
          dataSourceId: parseInt(dataSourceId),
        },
        query: {
          page: "1",
          limit: "200",
        },
      },
    },
    {
      placeholderData: keepPreviousData,
    },
  )
  const { handleDelete, isPending } = useDeletePrompt()

  if (error) {
    return <ErrorResult message={error.message} />
  }

  const edit = externalApiId != null ? data?.items.find((item) => item.id === parseInt(externalApiId)) : undefined

  // In order to show the base URL in the edit/create form, we need to pass it from the editing data or first item in the list
  const baseUrl = edit?.baseUrl || data?.items[0]?.baseUrl

  return (
    <RouteFocusModal>
      <RouteFocusModal.Header />
      <RouteFocusModal.Body className="flex h-full divide-x overflow-hidden">
        <div className="flex w-[260px] flex-col">
          <div className="flex h-[var(--topbar-height)] items-center justify-between px-4 py-2">
            <Heading level="h3">API Endpoints</Heading>
            <IconButton size="xsmall" variant="primary" onClick={() => navigateExternalApis("create")}>
              <IconPlus className="size-4" />
            </IconButton>
          </div>
          <Divider orientation="horizontal" />
          {isLoading && (
            <div className="flex flex-col gap-4">
              {Array.from({ length: 4 }).map((_, index) => (
                <TextSkeleton key={index} characters={20} />
              ))}
            </div>
          )}
          <div className="flex flex-1 flex-col gap-2 overflow-y-auto p-2">
            {data?.items?.map((item) => (
              <div
                key={item.id}
                className={cn(
                  "flex shrink-0 cursor-pointer items-center justify-between overflow-hidden rounded-md py-0.5 pe-1 ps-2 transition-colors hover:bg-ui-bg-subtle-hover",
                  externalApiId != null && `${item.id}` === externalApiId
                    ? "text-bg-ui-fg-on-color bg-ui-bg-highlight shadow-sm hover:bg-ui-bg-highlight"
                    : "",
                )}
                aria-disabled={isPending}
                onClick={() => navigateExternalApis(`${item.id}`)}
              >
                <Text size="xsmall" weight="regular" className="truncate">
                  {item.name}
                </Text>
                <IconButton
                  variant="transparent"
                  size="xsmall"
                  className="text-ui-fg-error opacity-0 transition-opacity group-hover:opacity-100"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDelete({
                      teamId: parseInt(teamId),
                      miniAppId: parseInt(miniAppId),
                      dataSourceId: parseInt(dataSourceId || ""),
                      externalApiId: item.id,
                      onSuccess: () => {
                        if (externalApiId === `${item.id}`) {
                          navigateExternalApis("create")
                        }
                      },
                    })
                  }}
                  isLoading={isPending}
                >
                  <Trash />
                </IconButton>
              </div>
            ))}
          </div>
          {data?.items.length === 0 && <Text>{t("emptyState.list.empty.description")}</Text>}
        </div>
        <div className="flex-1">
          {edit ? (
            <ApiEndpointCreateForm baseUrl={baseUrl} data={edit} key={edit.id} />
          ) : (
            <ApiEndpointCreateForm
              baseUrl={baseUrl}
              key="create"
              onSuccess={(data) => navigateExternalApis(`${data.id}`)}
            />
          )}
        </div>
      </RouteFocusModal.Body>
    </RouteFocusModal>
  )
}

export const useDeletePrompt = () => {
  const { t } = useTranslation()
  const prompt = usePrompt()

  const { mutate, isPending } = safQuery.useMutation(
    "delete",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}",
  )

  const handleDelete = useCallback(
    async ({
      teamId,
      miniAppId,
      dataSourceId,
      externalApiId,
      onSuccess,
    }: {
      teamId: number
      miniAppId: number
      dataSourceId: number
      externalApiId: number
      onSuccess?: () => void
    }) => {
      const confirm = await prompt({
        title: t("general.areYouSure"),
        description: t("general.areYouSureDescription", {
          entity: "Endpoint",
        }),
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel"),
      })

      if (!confirm) {
        return
      }

      mutate(
        {
          params: {
            path: {
              teamId,
              miniAppId,
              dataSourceId,
              externalApiId,
            },
          },
        },
        {
          onSuccess: () => {
            toast.success(t("operationFeedback.deleteSuccess"))
            queryClient.invalidateQueries(
              safQuery.queryOptions(
                "get",
                "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis",
                {
                  params: {
                    path: {
                      teamId,
                      miniAppId,
                      dataSourceId,
                    },
                  },
                },
              ),
            )
            onSuccess?.()
          },
          onError: (error) => {
            toast.error(error.message)
          },
        },
      )
    },
    [prompt, t, mutate],
  )

  return { handleDelete, isPending }
}
