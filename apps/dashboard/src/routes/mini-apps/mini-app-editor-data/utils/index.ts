import { components } from "@saf/sdk"
import { BadgeProps } from "@ui/components/badge"

export const getHttpMethodBadgeColor = (
  method: components["schemas"]["ExternalApiResponse"]["httpMethod"],
): BadgeProps["color"] => {
  switch (method.toUpperCase()) {
    case "GET":
      return "blue"
    case "POST":
      return "green"
    case "PUT":
      return "orange"
    case "DELETE":
      return "red"
    case "PATCH":
      return "purple"
    default:
      return "grey"
  }
}
