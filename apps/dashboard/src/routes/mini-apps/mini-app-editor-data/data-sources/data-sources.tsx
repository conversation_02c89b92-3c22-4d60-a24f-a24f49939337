import { safQuery } from "@/client"
import { NoRecords } from "@/components/common/empty-table-content"
import { ErrorResult } from "@/components/common/error-result"
import { Loader } from "@/components/common/loader"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { IconDatabase } from "@tabler/icons-react"
import { useParams } from "react-router-dom"
import { ExternalApis } from "../external-apis/external-apis"
import { Text } from "@saf/ui"

export const DataSources = () => {
  const { teamId = "", miniAppId = "", dataSourceId } = useParams()
  const { data, isLoading, isError, error } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
          dataSourceId: parseInt(dataSourceId || ""),
        },
      },
    },
    {
      enabled: dataSourceId != null,
    },
  )

  if (isLoading) {
    return (
      <div className="grid h-full flex-col place-items-center p-4">
        <Loader />
      </div>
    )
  }

  if (isError) {
    return <ErrorResult message={showHumanFriendlyError(error)} />
  }

  if (!data) {
    return (
      <div className="grid size-full flex-1 place-items-center">
        <NoRecords
          icon={<IconDatabase className="text-ui-fg-subtle" />}
          title="No data source selected"
          message="Select a data source on the left or create a new one."
        />
      </div>
    )
  }

  return data.dataSourceType === "external_api" ? (
    <ExternalApis data={data} />
  ) : (
    <div className="grid h-full place-items-center">
      <Text className="text-center">Internal is not supported yet</Text>
    </div>
  )
}
