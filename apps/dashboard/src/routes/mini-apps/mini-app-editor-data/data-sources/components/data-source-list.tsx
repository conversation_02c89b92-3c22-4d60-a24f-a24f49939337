import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { Error<PERSON><PERSON>ult } from "@/components/common/error-result"
import { TextSkeleton } from "@/components/common/skeleton"
import { cn } from "@/lib/utils"
import { Trash } from "@medusajs/icons"
import { IconButton, Text, toast, usePrompt } from "@saf/ui"
import { keepPreviousData } from "@tanstack/react-query"
import { useCallback, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate, useParams } from "react-router-dom"

export const DataSourceList = () => {
  const { t } = useTranslation()
  const { teamId = "", miniAppId = "", versionId = "", dataSourceId } = useParams()
  const navigate = useNavigate()

  const openDataSourceDetail = (id: number | null) => {
    if (id) {
      navigate(`/teams/${teamId}/mini-apps/${miniAppId}/versions/${versionId}/data-sources/${id}`)
      localStorage.setItem("lastSavedDataSourceId", `${id}`)
    } else {
      navigate(`/teams/${teamId}/mini-apps/${miniAppId}/versions/${versionId}/data-sources`)
      localStorage.removeItem("lastSavedDataSourceId")
    }
  }

  const { data, isLoading, error } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
        },
        query: {
          page: "1",
          limit: "200",
        },
      },
    },
    {
      placeholderData: keepPreviousData,
    },
  )

  useEffect(() => {
    if (dataSourceId != null) return

    const lastSavedDataSourceId = localStorage.getItem("lastSavedDataSourceId")

    if (lastSavedDataSourceId == null) return

    const found = data?.items?.find((item) => `${item.id}` === lastSavedDataSourceId)

    if (found) {
      navigate(`${found.id}`)
    }
  }, [data?.items, dataSourceId, navigate])

  const { handleDelete, isPending } = useDeletePrompt()

  if (error) {
    return <ErrorResult message={error.message} />
  }

  return (
    <>
      {isLoading && (
        <div className="flex flex-col gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <TextSkeleton key={index} characters={20} />
          ))}
        </div>
      )}
      <div className="flex flex-col gap-2">
        {data?.items.map((item) => (
          <div
            key={item.id}
            className={cn(
              "group flex cursor-pointer items-center justify-between rounded-md border border-transparent py-2 pe-1 ps-2 transition-colors hover:bg-ui-bg-subtle-hover",
              dataSourceId != null && `${item.id}` === dataSourceId
                ? "border-ui-border-base bg-ui-bg-base shadow-sm hover:bg-ui-bg-base"
                : "",
            )}
            aria-disabled={isPending}
            onClick={() => openDataSourceDetail(item.id)}
          >
            <Text size="small" weight="regular">
              {item.name}
            </Text>
            <IconButton
              variant="transparent"
              size="xsmall"
              className="text-ui-fg-error opacity-0 transition-opacity group-hover:opacity-100"
              onClick={() =>
                handleDelete({
                  teamId: parseInt(teamId),
                  miniAppId: parseInt(miniAppId),
                  dataSourceId: item.id,
                  onSuccess: () => {
                    if (dataSourceId === `${item.id}`) {
                      openDataSourceDetail(null)
                    }
                  },
                })
              }
              isLoading={isPending}
            >
              <Trash />
            </IconButton>
          </div>
        ))}
      </div>
      {data?.items.length === 0 && <Text>{t("emptyState.list.empty.description")}</Text>}
    </>
  )
}

const useDeletePrompt = () => {
  const { t } = useTranslation()
  const prompt = usePrompt()

  const { mutate, isPending } = safQuery.useMutation(
    "delete",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}",
  )

  const handleDelete = useCallback(
    async ({
      teamId,
      miniAppId,
      dataSourceId,
      onSuccess,
    }: {
      teamId: number
      miniAppId: number
      dataSourceId: number
      onSuccess?: () => void
    }) => {
      const confirm = await prompt({
        title: t("general.areYouSure"),
        description: t("general.areYouSureDescription", {
          entity: "Data Source",
        }),
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel"),
      })

      if (!confirm) {
        return
      }

      mutate(
        {
          params: {
            path: {
              teamId,
              miniAppId,
              dataSourceId,
            },
          },
        },
        {
          onSuccess: () => {
            toast.success(t("operationFeedback.deleteSuccess"))
            queryClient.invalidateQueries(
              safQuery.queryOptions("get", "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources", {
                params: {
                  path: {
                    teamId,
                    miniAppId,
                  },
                },
              }),
            )
            onSuccess?.()
          },
          onError: (error) => {
            toast.error(error.message)
          },
        },
      )
    },
    [prompt, t, mutate],
  )

  return { handleDelete, isPending }
}
