import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { dataSourceTypeOptions } from "@/configs"
import { i18n } from "@/i18n/i18n"
import { createEnumFromOptions } from "@/lib/conversion"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { zodResolver } from "@hookform/resolvers/zod"
import { Alert, Button, Heading, Input, RadioGroup, toast } from "@saf/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import { useNavigate, useParams } from "react-router-dom"
import { z } from "zod"

const dataSourceCreateFormSchema = z.object({
  name: z.string().min(1, { message: i18n.t("validation.required") }),
  dataSourceType: z.enum(createEnumFromOptions(dataSourceTypeOptions)),
})

export const DataSourceCreateForm = ({ onSuccess }: { onSuccess?: () => void }) => {
  const { t } = useTranslation()
  const { teamId = "", miniAppId = "", versionId = "" } = useParams()
  const navigate = useNavigate()

  const form = useForm<z.infer<typeof dataSourceCreateFormSchema>>({
    resolver: zodResolver(dataSourceCreateFormSchema),
    defaultValues: {
      name: "",
      dataSourceType: dataSourceTypeOptions[1].value,
    },
  })

  const { mutateAsync, isPending } = safQuery.useMutation(
    "post",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources",
  )

  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      {
        params: {
          path: {
            teamId: parseInt(teamId),
            miniAppId: parseInt(miniAppId),
          },
        },
        body: {
          name: values.name,
          dataSourceType: values.dataSourceType,
          // TODO: Figure out how to handle this union type
          // @ts-expect-error Need to update the .tsp file in SDK project
          config: values.dataSourceType === "internal_table" ? {} : {},
        },
      },
      {
        onSuccess: (data) => {
          form.reset()
          toast.success(t("operationFeedback.createSuccess"))
          navigate(`/teams/${teamId}/mini-apps/${miniAppId}/versions/${versionId}/data-sources/${data.id}`)
          onSuccess?.()
          queryClient.invalidateQueries(
            safQuery.queryOptions("get", "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources", {
              params: {
                path: {
                  teamId: parseInt(teamId),
                  miniAppId: parseInt(miniAppId),
                },
              },
            }),
          )
        },
        onError: (error) => {
          form.setError("root", {
            type: "manual",
            message: showHumanFriendlyError(error),
          })
        },
      },
    )
  })

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="flex flex-col gap-y-4 p-4">
        <Heading level="h2">Create Data Source</Heading>
        {form.formState.errors.root && (
          <Alert variant="error" dismissible={false} className="text-balance">
            {form.formState.errors.root.message}
          </Alert>
        )}
        <div className="flex flex-col gap-y-4">
          <Field {...form} name="name" label={t("fields.name")}>
            <Input type="text" />
          </Field>
          <Form.Field
            control={form.control}
            name="dataSourceType"
            render={({ field }) => {
              return (
                <Form.Item>
                  <Form.Label>Type</Form.Label>
                  <Form.Control>
                    <RadioGroup
                      className="flex-col gap-y-3"
                      {...field}
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      {dataSourceTypeOptions.map((option) => (
                        <RadioGroup.ChoiceBox key={option.value} value={option.value} label={option.label} />
                      ))}
                    </RadioGroup>
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
          <div className="flex items-center justify-end">
            <Button size="small" type="submit" isLoading={isPending}>
              {t("actions.create")}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  )
}
