import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { MiniAppVersionDetail } from "../stores/mini-app-design-store"
import { createMiniAppVersionDetailQueryOptions } from "./use-pages"

export const useAddWidget = () => {
  return safQuery.useMutation(
    "post",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets",
    {
      onSuccess: (newWidget, req) => {
        const detailQueryOption = createMiniAppVersionDetailQueryOptions(req.params.path)

        queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => ({
          ...old,
          pages:
            old?.pages?.map((page) => {
              if (page.id === req.params.path.pageId) {
                return {
                  ...page,
                  widgets: [...(page.widgets || []), newWidget],
                }
              }
              return page
            }) || [],
        }))
      },
    },
  )
}

export const useUpdateWidget = () => {
  return safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets/{widgetId}",
    {
      onSuccess: (updatedWidget, req) => {
        const { teamId, miniAppId, versionId } = req.params.path
        const detailQueryOption = createMiniAppVersionDetailQueryOptions({
          teamId,
          miniAppId,
          versionId,
        })

        queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => {
          return {
            ...old,
            pages:
              old?.pages?.map((page) => {
                if (page.id === req.params.path.pageId) {
                  return {
                    ...page,
                    widgets: page.widgets?.map((widget) =>
                      widget.id === req.params.path.widgetId ? updatedWidget : widget,
                    ),
                  }
                }
                return page
              }) || [],
          }
        })
      },
    },
  )
}

export const useMoveWidget = () => {
  return safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets/{widgetId}/move",
  )
}

export const useDeleteWidget = () => {
  return safQuery.useMutation(
    "delete",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets/{widgetId}",
    {
      onSuccess: (_, req) => {
        const { teamId, miniAppId, versionId, pageId } = req.params.path
        const detailQueryOption = createMiniAppVersionDetailQueryOptions({
          teamId,
          miniAppId,
          versionId,
        })

        queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => ({
          ...old,
          pages:
            old?.pages?.map((page) => {
              if (page.id === pageId) {
                return {
                  ...page,
                  widgets: page.widgets?.filter((widget) => widget.id !== req.params.path.widgetId),
                }
              }
              return page
            }) || [],
        }))
      },
    },
  )
}
