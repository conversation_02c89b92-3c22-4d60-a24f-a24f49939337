import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"

export const createMiniAppVersionDetailQueryOptions = ({
  teamId,
  miniAppId,
  versionId,
}: {
  teamId: number
  miniAppId: number
  versionId: number
}) => {
  return safQuery.queryOptions("get", "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}", {
    params: {
      path: {
        teamId: teamId,
        miniAppId: miniAppId,
        versionId: versionId,
      },
    },
  })
}

export const useMiniAppVersionDetail = ({
  teamId,
  miniAppId,
  versionId,
}: {
  teamId: number
  miniAppId: number
  versionId: number
}) => {
  return safQuery.useQuery("get", "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}", {
    params: {
      path: {
        teamId: teamId,
        miniAppId: miniAppId,
        versionId: versionId,
      },
    },
  })
}

type MiniAppVersionDetail = Awaited<ReturnType<typeof useMiniAppVersionDetail>>["data"]

export const useCreatePage = () => {
  return safQuery.useMutation(
    "post",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages",
    {
      onSuccess: (newPage, req) => {
        const detailQueryOption = createMiniAppVersionDetailQueryOptions(req.params.path)

        queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => ({
          ...old,
          pages: [...(old?.pages || []), newPage],
        }))
      },
    },
  )
}

export const useUpdatePage = () => {
  return safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}",
    {
      onSuccess: (updatedPage, req) => {
        const { teamId, miniAppId, versionId } = req.params.path
        const detailQueryOption = createMiniAppVersionDetailQueryOptions({
          teamId,
          miniAppId,
          versionId,
        })

        queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => {
          return {
            ...old,
            pages: old?.pages?.map((page) => (page.id === updatedPage.id ? { ...page, ...updatedPage } : page)) || [],
          }
        })
      },
    },
  )
}

export const useMovePage = () => {
  return safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/move",
  )
}

export const useDeletePage = () => {
  return safQuery.useMutation(
    "delete",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}",
    {
      onSuccess: (_, req) => {
        const { teamId, miniAppId, versionId, pageId } = req.params.path
        const detailQueryOption = createMiniAppVersionDetailQueryOptions({
          teamId,
          miniAppId,
          versionId,
        })

        queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => ({
          ...old,
          pages: old?.pages?.filter((page) => page.id !== pageId) || [],
        }))
      },
    },
  )
}
