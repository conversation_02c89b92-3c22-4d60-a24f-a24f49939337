import { components } from "@saf/sdk"
import { create } from "zustand"

export type MiniAppVersionDetail = components["schemas"]["MiniAppVersionDetail"]
export type Page = MiniAppVersionDetail["pages"][0]
export type Widget = NonNullable<Page["widgets"]>[0]

type MiniAppDesignStoreState = {
  selectedPageId: number | null
  selectedWidgetId: number | null
}

type MiniAppDesignAction = {
  setSelectedPageId: (pageId?: number | null) => void
  setSelectedWidgetId: (widgetId?: number | null) => void
}

export const useMiniAppDesignStore = create<MiniAppDesignStoreState & MiniAppDesignAction>((set) => ({
  selectedPageId: null,
  selectedWidgetId: null,
  setSelectedPageId: (pageId) => set({ selectedPageId: pageId }),
  setSelectedWidgetId: (widgetId) => set({ selectedWidgetId: widgetId }),
}))
