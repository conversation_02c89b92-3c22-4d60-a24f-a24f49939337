import { queryClient } from "@/client/react-query"
import { Loader } from "@/components/common/loader"
import { SortableBaseItem, SortableList, SortableListProps } from "@/components/common/sortable-list"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { cn } from "@/lib/utils"
import { DotsSix } from "@medusajs/icons"
import { Button, IconButton, Text } from "@saf/ui"
import { IconPlus } from "@tabler/icons-react"
import { CreatePagePopover } from "../components/create-page-popover"
import { createMiniAppVersionDetailQueryOptions, useMiniAppVersionDetail, useMovePage } from "../hooks/use-pages"
import { MiniAppVersionDetail, Page, useMiniAppDesignStore } from "../stores/mini-app-design-store"

export const PageList = ({
  teamId,
  miniAppId,
  versionId,
}: {
  teamId: string
  miniAppId: string
  versionId: string
}) => {
  const { data, isLoading, error } = useMiniAppVersionDetail({
    teamId: parseInt(teamId),
    miniAppId: parseInt(miniAppId),
    versionId: parseInt(versionId),
  })

  const movePage = useMovePage()

  const {
    selectedPageId: _selectedPageId,
    setSelectedPageId,
    selectedWidgetId: _selectedWidgetId,
    setSelectedWidgetId,
  } = useMiniAppDesignStore()

  if (isLoading) {
    return <Loader size="small" />
  }

  if (error) {
    return <p className="text-ui-button-danger">{showHumanFriendlyError(error)}</p>
  }

  if (!data) {
    return <p className="text-ui-fg-muted">No data found</p>
  }

  const selectedPage: Page | undefined = data?.pages?.find((page) => page.id === _selectedPageId) || data.pages[0]

  const sortedPages = data.pages.sort((a, b) => a.position - b.position)

  const handlePositionChange: SortableListProps<SortableBaseItem>["onChange"] = (newArray, dragEndEvent) => {
    const { active, over } = dragEndEvent
    if (!over || active.id === over.id) return

    const originalPages = [...data.pages]

    const newIndex = sortedPages.findIndex(({ id }) => id === over.id)

    const detailQueryOption = createMiniAppVersionDetailQueryOptions({
      teamId: parseInt(teamId),
      miniAppId: parseInt(miniAppId),
      versionId: parseInt(versionId),
    })

    // Optimistically update the query data to reflect the new order
    queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => {
      const positionUpdatedPages = newArray.map((item, index) => ({
        ...item,
        position: index + 1,
      }))

      return {
        ...old,
        pages: positionUpdatedPages,
      }
    })

    movePage.mutate(
      {
        body: {
          // +1 because positions are 1-based in the API
          targetPosition: newIndex + 1,
        },
        params: {
          path: {
            teamId: parseInt(teamId),
            miniAppId: parseInt(miniAppId),
            versionId: parseInt(versionId),
            pageId: parseInt(dragEndEvent.active.id as string),
          },
        },
      },
      {
        onError: (_, req) => {
          // Revert the optimistic update if the mutation fails
          queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => {
            return {
              ...old,
              pages: originalPages,
            }
          })
          showHumanFriendlyError(req)
        },
      },
    )
  }

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center justify-between gap-2 px-3.5">
        <Text size="xsmall" className="uppercase" weight="plus">
          Pages
        </Text>
        <CreatePagePopover
          teamId={teamId}
          miniAppId={miniAppId}
          versionId={versionId}
          pageCount={data.pages.length}
          trigger={
            <IconButton size="xsmall">
              <IconPlus className="size-4" />
            </IconButton>
          }
          onSuccess={(page) => {
            setSelectedPageId(page.id)
            setSelectedWidgetId(null)
          }}
        />
      </div>
      <div className="space-y-1.5 px-2 py-1">
        {data.pages.length === 0 && (
          <Text className="px-1.5" size="xsmall" color="subtle">
            Start adding pages to your mini app
          </Text>
        )}
        <SortableList
          className="gap-2"
          items={sortedPages}
          onChange={handlePositionChange}
          renderItem={(page) => {
            return (
              <SortableList.Item id={page.id}>
                <PageItem
                  page={page}
                  selectedPage={selectedPage}
                  setSelectedPageId={setSelectedPageId}
                  setSelectedWidgetId={setSelectedWidgetId}
                />
              </SortableList.Item>
            )
          }}
        />
      </div>
    </div>
  )
}

export const PageItem = ({
  page,
  selectedPage,
  setSelectedPageId,
  setSelectedWidgetId,
}: {
  page: Page
  selectedPage: Page | undefined
  setSelectedPageId: (id?: number) => void
  setSelectedWidgetId: (id?: number) => void
}) => {
  const { attributes, listeners, ref } = SortableList.useSortableItemContext()

  return (
    <Button
      size="large"
      type="button"
      variant={page.id === selectedPage?.id ? "secondary" : "transparent"}
      key={page.id}
      onClick={() => {
        setSelectedPageId(page.id)
        // Reset selected widget when a new page is selected
        setSelectedWidgetId(undefined)
      }}
      {...attributes}
      {...listeners}
      ref={ref}
      className={cn("w-full justify-start p-1 pe-2 ps-1 text-start [&>svg]:size-4", page.isHidden && "opacity-50")}
    >
      <DotsSix className="size-3 cursor-grab touch-none text-ui-fg-muted active:cursor-grabbing" />
      <Text size="xsmall" leading="compact" className="truncate">
        {page.name}
      </Text>
    </Button>
  )
}
