import { But<PERSON>, <PERSON>, toast, usePrompt } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { useDeleteWidget } from "../hooks/use-widgets"
import { cn } from "@/lib/utils"

export const DeleteWidgetSection = ({
  teamId,
  miniAppId,
  versionId,
  pageId,
  widgetId,
  className,
  onSuccess,
}: {
  teamId: string
  miniAppId: string
  versionId: string
  pageId: string
  widgetId: string
  className?: string
  onSuccess?: () => void
}) => {
  const { t } = useTranslation()
  const prompt = usePrompt()
  const { mutate, isPending } = useDeleteWidget()

  const handleDelete = async () => {
    const confirm = await prompt({
      title: t("general.areYouSure"),
      description: t("general.areYouSureDescription", {
        entity: "Widget",
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel"),
    })

    if (!confirm) {
      return
    }

    mutate(
      {
        params: {
          path: {
            teamId: parseInt(teamId),
            miniAppId: parseInt(miniAppId),
            versionId: parseInt(versionId),
            pageId: parseInt(pageId),
            widgetId: parseInt(widgetId),
          },
        },
      },
      {
        onSuccess: () => {
          toast.success(t("operationFeedback.deleteSuccess"))
          onSuccess?.()
        },
        onError: (error) => {
          toast.error(error.message)
        },
      },
    )
  }

  return (
    <div className={cn("flex items-center justify-between gap-2", className)}>
      <Text size="small">Danger Zone</Text>
      <Button size="small" variant="danger" onClick={handleDelete} isLoading={isPending}>
        Delete Widget
      </Button>
    </div>
  )
}
