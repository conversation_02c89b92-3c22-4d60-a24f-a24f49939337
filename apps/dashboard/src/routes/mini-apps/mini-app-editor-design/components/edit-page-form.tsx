import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { i18n } from "@/i18n/i18n"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button, Checkbox, Divider, Input, Text, toast, usePrompt } from "@saf/ui"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import { useDebouncedCallback } from "use-debounce"
import { z } from "zod"
import { useDeletePage, useUpdatePage } from "../hooks/use-pages"
import { Page } from "../stores/mini-app-design-store"
import { Loader } from "@/components/common/loader"

const editPageFormSchema = z.object({
  pageName: z.string().min(1, { message: i18n.t("validation.required") }),
  title: z.string().optional(),
  icon: z.string().optional(),
  hideInNavbar: z.boolean(),
  isHidden: z.boolean(),
})

type EditPageSchema = z.infer<typeof editPageFormSchema>

export const EditPageForm = ({
  teamId,
  miniAppId,
  versionId,
  page,
  onDelete,
}: {
  teamId: string
  miniAppId: string
  versionId: string
  page: Page
  onDelete?: () => void
}) => {
  const updatePage = useUpdatePage()

  const debouncedUpdate = useDebouncedCallback((values: Partial<EditPageSchema>) => {
    updatePage.mutate(
      {
        body: {
          name: values.pageName,
          title: values.title,
          hideInNavbar: values.hideInNavbar,
          icon: values.icon,
          isHidden: values.isHidden,
        },
        params: {
          path: {
            teamId: parseInt(teamId),
            miniAppId: parseInt(miniAppId),
            versionId: parseInt(versionId),
            pageId: page.id,
          },
        },
      },
      {
        onError: (error) => {
          toast.error(error.message || "Failed to update screen")
        },
      },
    )
  }, 500)

  const form = useForm<EditPageSchema>({
    resolver: zodResolver(editPageFormSchema),
    defaultValues: {
      pageName: "",
      title: "",
      icon: "",
      hideInNavbar: false,
      isHidden: false,
    },
  })

  useEffect(() => {
    if (page) {
      form.reset({
        pageName: page.name,
        title: page.title || "",
        icon: page.icon || "",
        hideInNavbar: page.hideInNavbar,
        isHidden: page.isHidden,
      })
    }
    // TODO: revise this dependency array to avoid unnecessary updates
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page])

  useEffect(() => {
    const subscription = form.watch((values) => {
      debouncedUpdate({
        pageName: values.pageName,
        title: values.title || "",
        icon: values.icon || "",
        hideInNavbar: values.hideInNavbar,
        isHidden: values.isHidden,
      })
    })

    return () => subscription.unsubscribe()
    // TODO: revise this dependency array to avoid unnecessary updates
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, form])

  if (!page) {
    return <Text>Screen not found</Text>
  }

  return (
    <>
      <Form {...form}>
        <form className="flex flex-1 flex-col gap-4 p-4">
          <div className="flex items-center justify-between gap-2">
            <Text className="uppercase" size="small">
              Page Settings
            </Text>
            {updatePage.isPending && <Loader size="small" />}
          </div>
          <Field control={form.control} name="pageName" label="Screen Name">
            <Input size="small" />
          </Field>

          <Field control={form.control} name="title" label="Display Title (optional)">
            <Input size="small" />
          </Field>

          <Field control={form.control} name="icon" label="Icon">
            <Input size="small" placeholder="https://example.com/icon.png" />
          </Field>

          <Form.Field
            control={form.control}
            name="hideInNavbar"
            render={({ field: { value, onChange, ...field } }) => {
              return (
                <Form.Item className="flex flex-row items-center gap-3">
                  <Form.Control>
                    <Checkbox checked={value} onCheckedChange={onChange} {...field} />
                  </Form.Control>
                  <Form.Label>Hide in Navigation Bar</Form.Label>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />

          <Form.Field
            control={form.control}
            name="isHidden"
            render={({ field: { value, onChange, ...field } }) => {
              return (
                <Form.Item className="flex flex-row items-center gap-3">
                  <Form.Control>
                    <Checkbox checked={value} onCheckedChange={onChange} {...field} />
                  </Form.Control>
                  <Form.Label>Hide Screen</Form.Label>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
        </form>
      </Form>
      <div className="mt-auto">
        <Divider orientation="horizontal" />
        <div className="flex items-center justify-between gap-2 p-4">
          <Text size="small">Danger Zone</Text>
          <DeleteButton
            teamId={parseInt(teamId)}
            miniAppId={parseInt(miniAppId)}
            versionId={parseInt(versionId)}
            pageId={page.id}
            onSuccess={() => {
              onDelete?.()
            }}
          />
        </div>
      </div>
    </>
  )
}

const DeleteButton = ({
  teamId,
  miniAppId,
  versionId,
  pageId,
  onSuccess,
}: {
  teamId: number
  miniAppId: number
  versionId: number
  pageId: number
  onSuccess?: () => void
}) => {
  const { t } = useTranslation()
  const prompt = usePrompt()
  const { mutate, isPending } = useDeletePage()

  const handleDelete = async () => {
    const confirm = await prompt({
      title: t("general.areYouSure"),
      description: t("general.areYouSureDescription", {
        entity: "Page",
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel"),
    })

    if (!confirm) {
      return
    }

    mutate(
      {
        params: {
          path: {
            teamId,
            miniAppId,
            versionId,
            pageId,
          },
        },
      },
      {
        onSuccess: () => {
          toast.success(t("operationFeedback.deleteSuccess"))
          onSuccess?.()
        },
        onError: (error) => {
          toast.error(error.message)
        },
      },
    )
  }

  return (
    <Button size="small" variant="danger" onClick={handleDelete} isLoading={isPending}>
      Delete Page
    </Button>
  )
}
