import { safQuery } from "@/client"
import { RouteFocusModalError } from "@/components/common/error-result"
import { ModalFormSectionSkeleton } from "@/components/common/skeleton"
import { useParams } from "react-router-dom"
import { RouteFocusModal } from "../../../components/modals"
import { CreateMiniAppForm } from "../mini-app-create/components/create-mini-app-form"

export const MiniAppEdit = () => {
  const { teamId = "", miniAppId = "" } = useParams()

  const { data, isLoading, error } = safQuery.useQuery("get", `/api/admin/teams/{teamId}/mini-apps/{miniAppId}`, {
    params: {
      path: {
        teamId: parseInt(teamId),
        miniAppId: parseInt(miniAppId),
      },
    },
  })

  return (
    <RouteFocusModal>
      {isLoading ? (
        <ModalFormSectionSkeleton fieldCount={5} />
      ) : error ? (
        <RouteFocusModalError message={error.message} />
      ) : (
        <CreateMiniAppForm miniApp={data} />
      )}
    </RouteFocusModal>
  )
}
