import { AnimatePresence } from "motion/react"
import { useEffect, useState } from "react"
import { Link, Outlet, useLocation, useNavigation, useParams } from "react-router-dom"

import AvatarBox from "@/components/common/logo-box/avatar-box"
import { ProgressBar } from "@/components/common/progress-bar"
import { useDevicePreviewStore } from "@/components/device-preview"
import { cn } from "@/lib/utils"
import { useTheme } from "@/providers/theme-provider"
import { CircleHalfSolid } from "@medusajs/icons"
import { Button, DropdownMenu, IconButton, Text, Tooltip } from "@saf/ui"
import {
  IconArrowBackUp,
  IconDatabase,
  IconDeviceMobile,
  IconDeviceTablet,
  IconLayout,
  IconSettings,
} from "@tabler/icons-react"
import { useTranslation } from "react-i18next"

import { safQuery } from "@/client"
import { ErrorResult } from "@/components/common/error-result"
import { Loader } from "@/components/common/loader"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { components } from "@saf/sdk"
import React from "react"

export const MiniAppEditor = () => {
  const { teamId = "", miniAppId = "", versionId = "" } = useParams()
  const { data, isLoading, error, isError } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
          versionId: parseInt(versionId),
        },
      },
    },
  )

  if (isLoading) {
    return (
      <div className="grid h-screen place-items-center">
        <Loader />
      </div>
    )
  }

  if (isError || !data) {
    return <ErrorResult message={showHumanFriendlyError(error)} />
  }

  return <MiniAppEditorContent data={data} />
}

export const MiniAppEditorContent = ({ data }: { data: components["schemas"]["MiniAppVersion"] }) => {
  const navigation = useNavigation()

  const loading = navigation.state === "loading"

  return (
    <div
      className="relative flex h-screen flex-row flex-nowrap items-start overflow-hidden"
      style={
        {
          "--topbar-height": "3rem",
        } as React.CSSProperties
      }
    >
      <NavigationBar loading={loading} />
      <div className="flex h-screen w-16 flex-col items-center gap-2 border-r">
        <div className="grid h-[var(--topbar-height)] place-items-center">
          <AvatarBox size="large" className="m-0" />
        </div>
        <Nav />
        <div className="m-2 mt-auto">
          <ThemeToggle />
        </div>
      </div>
      <div className="flex h-screen w-full flex-col overflow-auto">
        <Topbar data={data} />
        <main
          className={cn(
            "w-full flex-1 flex-col items-center overflow-y-auto transition-opacity delay-200 duration-200",
            {
              "opacity-25": loading,
            },
          )}
        >
          <Outlet />
        </main>
      </div>
    </div>
  )
}

const NavigationBar = ({ loading }: { loading: boolean }) => {
  const [showBar, setShowBar] = useState(false)

  /**
   * If the loading state is true, we want to show the bar after a short delay.
   * The delay is used to prevent the bar from flashing on quick navigations.
   */
  useEffect(() => {
    let timeout: ReturnType<typeof setTimeout>

    if (loading) {
      timeout = setTimeout(() => {
        setShowBar(true)
      }, 200)
    } else {
      setShowBar(false)
    }

    return () => {
      clearTimeout(timeout)
    }
  }, [loading])

  return (
    <div className="fixed inset-x-0 top-0 z-50 h-1">
      <AnimatePresence>{showBar ? <ProgressBar /> : null}</AnimatePresence>
    </div>
  )
}

const Topbar = ({ data }: { data: components["schemas"]["MiniAppVersion"] }) => {
  const { mode, setMode } = useDevicePreviewStore()
  const location = useLocation()

  return (
    <div className="grid h-[var(--topbar-height)] w-full grid-cols-2 border-b px-3 py-2">
      <div className="flex shrink-0 items-center gap-x-6">
        <IconButton asChild variant="secondary">
          <Link to="./../..">
            <IconArrowBackUp className="size-4" />
          </Link>
        </IconButton>
        <Text className="transition-fg hover:text-ui-fg-subtle">{data.version}</Text>
        {location.pathname.endsWith("design") && (
          <div className="absolute left-1/2 flex gap-1.5 rounded-lg border border-ui-border-base bg-ui-bg-switch-off p-1">
            <Tooltip content="Toggle Mobile View">
              <IconButton
                size="xsmall"
                onClick={() => setMode("mobile")}
                variant={mode === "mobile" ? "secondary" : "transparent"}
              >
                <IconDeviceMobile className="size-4" />
              </IconButton>
            </Tooltip>
            <Tooltip content="Toggle Tablet View">
              <IconButton
                size="xsmall"
                onClick={() => setMode("tablet")}
                variant={mode === "tablet" ? "secondary" : "transparent"}
              >
                <IconDeviceTablet className="size-4" />
              </IconButton>
            </Tooltip>
          </div>
        )}
      </div>
      <div className="flex items-center justify-end gap-x-3">
        {/* <MiniAppVersionSelector /> */}
        <MiniAppPublishAction />
      </div>
    </div>
  )
}

// type MiniAppVersionStatus = "Draft" | "In Review" | "Approved" | "Rejected" | "Live"

const MiniAppPublishAction = () => {
  return (
    <Button size="small" variant="primary">
      Publish
    </Button>
  )

  // const status: MiniAppVersionStatus = "Rejected"

  // return (
  //   <div className="space-x-2">
  //     {status === "Draft" && (
  //       <Button size="small" variant="primary">
  //         Send for Review
  //       </Button>
  //     )}
  //     {status === "In Review" && (
  //       <Button size="small" variant="danger">
  //         Cancel Review
  //       </Button>
  //     )}
  //     {status === "Approved" && (
  //       <Button size="small" variant="primary">
  //         Publish
  //       </Button>
  //     )}
  //     {status === "Rejected" && (
  //       <Button size="small" variant="primary">
  //         Update
  //       </Button>
  //     )}
  //     {status === "Live" && (
  //       <>
  //         <Button size="small" variant="secondary" disabled>
  //           Published
  //           <Tooltip content="Published on 2023-10-01">
  //             <IconInfoCircle className="size-4" />
  //           </Tooltip>
  //         </Button>
  //         <Button size="small" variant="primary">
  //           Update
  //           <IconPlus className="size-4" />
  //         </Button>
  //       </>
  //     )}
  //   </div>
  // );
}

// const MiniAppVersionSelector = () => {
//   return (
//     <div className="w-[160px]">
//       <Select size="small">
//         <Select.Trigger>
//           <Select.Value placeholder="Select version" />
//         </Select.Trigger>
//         <Select.Content>
//           {[
//             {
//               value: "0.0.1",
//               status: "Live",
//             },
//             {
//               value: "0.0.2",
//               status: "Draft",
//             },
//             {
//               value: "0.0.3",
//               status: "Rejected",
//             },
//           ].map((item) => (
//             <Select.Item key={item.value} value={item.value}>
//               <div className="flex max-w-none flex-row items-center gap-2 p-0">
//                 <span>{item.value}</span>
//                 {/* <StatusBadge
//                   color={
//                     {
//                       Live: "green",
//                       Draft: "orange",
//                       Rejected: "red",
//                       "": "grey",
//                     }[item.status || ""]
//                   }
//                 >
//                   {item.status}
//                 </StatusBadge> */}
//               </div>
//             </Select.Item>
//           ))}
//         </Select.Content>
//       </Select>
//     </div>
//   )
// }

const Nav = () => {
  const location = useLocation()

  return (
    <div className="space-y-3">
      {[
        {
          label: "Data",
          to: "data-sources",
          icon: <IconDatabase />,
        },
        {
          label: "Design",
          to: "design",
          icon: <IconLayout />,
        },
        {
          label: "Settings",
          to: "settings",
          icon: <IconSettings />,
        },
      ].map((item) => {
        const isActive = location.pathname.includes(item.to)

        return (
          <Link
            key={item.to}
            to={item.to}
            className={`flex flex-col items-center justify-center gap-1 text-xs [&_svg]:size-5 ${
              isActive ? "text-ui-fg-primary" : "text-ui-fg-muted"
            }`}
          >
            <Button
              size="base"
              variant={isActive ? "secondary" : "transparent"}
              className={cn("h-10", isActive ? "dark:bg-gray-600" : "bg-gray-200/60 dark:bg-gray-800")}
            >
              {item.icon}
            </Button>
            {item.label}
          </Link>
        )
      })}
    </div>
  )
}

export const ThemeToggle = () => {
  const { t } = useTranslation()
  const { theme, setTheme } = useTheme()

  return (
    <DropdownMenu>
      <DropdownMenu.Trigger className="rounded-md" asChild>
        <IconButton size="large" variant="transparent">
          <CircleHalfSolid className="text-ui-fg-subtle" />
        </IconButton>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content>
        <DropdownMenu.RadioGroup value={theme}>
          <DropdownMenu.RadioItem
            value="system"
            onClick={(e) => {
              e.preventDefault()
              setTheme("system")
            }}
          >
            {t("app.menus.user.theme.system")}
          </DropdownMenu.RadioItem>
          <DropdownMenu.RadioItem
            value="light"
            onClick={(e) => {
              e.preventDefault()
              setTheme("light")
            }}
          >
            {t("app.menus.user.theme.light")}
          </DropdownMenu.RadioItem>
          <DropdownMenu.RadioItem
            value="dark"
            onClick={(e) => {
              e.preventDefault()
              setTheme("dark")
            }}
          >
            {t("app.menus.user.theme.dark")}
          </DropdownMenu.RadioItem>
        </DropdownMenu.RadioGroup>
      </DropdownMenu.Content>
    </DropdownMenu>
  )
}
