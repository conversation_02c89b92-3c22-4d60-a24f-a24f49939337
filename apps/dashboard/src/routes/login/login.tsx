import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod"
import { <PERSON><PERSON>, Button, Heading, Hint, Input, Text } from "@saf/ui"
import { useForm } from "react-hook-form"
import { Trans, useTranslation } from "react-i18next"
import { Link, useLocation, useNavigate } from "react-router-dom"
import * as z from "zod"

import { LogoBox } from "@/components/common/logo-box"
import { useLogin } from "@/hooks/auth"
import { Form } from "@/components/common/form"

const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address").min(1, "Email is required"),
  password: z.string().min(1, "Password is required"),
})

type LoginSchema = z.infer<typeof loginSchema>

export const Login = () => {
  const { t } = useTranslation()
  const location = useLocation()
  const navigate = useNavigate()

  const from = location.state?.from?.pathname || "/"

  const form = useForm<LoginSchema>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "<EMAIL>",
      password: "Foo@1234",
    },
  })

  const { mutateAsync, isPending } = useLogin()

  const handleSubmit = form.handleSubmit(async ({ email, password }) => {
    await mutateAsync(
      {
        body: {
          email,
          password,
        },
      },
      {
        onError: (error) => {
          form.setError("root.serverError", {
            type: "manual",
            message: error?.message,
          })
        },
        onSuccess: () => {
          navigate(from, { replace: true })
        },
      },
    )
  })

  const serverError = form.formState.errors?.root?.serverError?.message
  const validationError = form.formState.errors.email?.message || form.formState.errors.password?.message

  return (
    <div className="flex min-h-dvh w-dvw items-center justify-center bg-ui-bg-subtle">
      <div className="m-4 flex w-full max-w-[280px] flex-col items-center">
        <LogoBox className="mb-4" />
        <div className="mb-4 flex flex-col items-center">
          <Heading>{t("login.title")}</Heading>
          <Text size="small" className="text-center text-ui-fg-subtle">
            {t("login.hint")}
          </Text>
        </div>
        <div className="flex w-full flex-col gap-y-3">
          <Form {...form}>
            <form onSubmit={handleSubmit} className="flex w-full flex-col gap-y-6">
              <div className="flex flex-col gap-y-1">
                <Form.Field
                  control={form.control}
                  name="email"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Control>
                          <Input
                            autoComplete="email"
                            {...field}
                            className="bg-ui-bg-field-component"
                            placeholder={t("fields.email")}
                          />
                        </Form.Control>
                      </Form.Item>
                    )
                  }}
                />
                <Form.Field
                  control={form.control}
                  name="password"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Label>{}</Form.Label>
                        <Form.Control>
                          <Input
                            type="password"
                            autoComplete="current-password"
                            {...field}
                            className="bg-ui-bg-field-component"
                            placeholder={t("fields.password")}
                          />
                        </Form.Control>
                      </Form.Item>
                    )
                  }}
                />
              </div>
              {validationError && (
                <div className="text-center">
                  <Hint className="inline-flex" variant={"error"}>
                    {validationError}
                  </Hint>
                </div>
              )}
              {serverError && (
                <Alert className="items-center bg-ui-bg-base p-2" dismissible variant="error">
                  {serverError}
                </Alert>
              )}
              <Button className="w-full" type="submit" isLoading={isPending}>
                {t("actions.continueWithEmail")}
              </Button>
            </form>
          </Form>
        </div>
        <span className="txt-small my-6 text-ui-fg-muted">
          <Trans
            i18nKey="login.forgotPassword"
            components={[
              <Link
                key="reset-password-link"
                to="/reset-password"
                className="font-medium text-ui-fg-interactive outline-none transition-fg hover:text-ui-fg-interactive-hover focus-visible:text-ui-fg-interactive-hover"
              />,
            ]}
          />
        </span>
      </div>
    </div>
  )
}
