import { SingleColumnPage } from "@/components/layout/pages"
import { useParams } from "react-router-dom"
import { TeamUserListTable } from "./components/team-user-list-table"
import { UserListTable } from "./components/user-list-table"

export const UserList = () => {
  const { teamId } = useParams()

  return <SingleColumnPage>{teamId == null ? <UserListTable /> : <TeamUserListTable />}</SingleColumnPage>
}
