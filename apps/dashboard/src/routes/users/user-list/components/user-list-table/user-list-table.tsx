import { Badge, Container, createDataTableColumnHelper, Text } from "@saf/ui"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"

import { safQuery } from "@/client"
import { DataTable, emptyDataTableValue } from "@/components/data-table"
import {
  DataTableStatusCell,
  DataTableStatusCellProps,
} from "@/components/data-table/components/data-table-status-cell/data-table-status-cell"
import { systemRoleOptions, teamRoleOptions, userStatusOptions } from "@/configs"
import { usePaginationQueryParam } from "@/hooks/use-pagination-query-params"
import { reduceOptionsToMap } from "@/lib/utils"
import { PencilSquare } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { keepPreviousData } from "@tanstack/react-query"

type UserResponse = components["schemas"]["UserResponse"]

const PAGE_SIZE = 20
const SEARCH_QUERY_KEY = "email"

export const UserListTable = () => {
  const { t } = useTranslation()
  const queryParams = usePaginationQueryParam([SEARCH_QUERY_KEY])

  const { data, isError, error, isPending } = safQuery.useQuery(
    "get",
    "/api/admin/users",
    {
      params: {
        query: queryParams,
      },
    },
    {
      placeholderData: keepPreviousData,
    },
  )

  const columns = useColumns()

  if (isError) {
    throw error
  }

  return (
    <Container className="divide-y p-0">
      <DataTable
        data={data?.items || emptyDataTableValue}
        columns={columns}
        getRowId={(row) => `${row.id}`}
        rowCount={data?.totalCount}
        pageSize={PAGE_SIZE}
        heading={t("users.domain")}
        rowHref={(row) => `${row.id}`}
        isLoading={isPending}
        searchQueryKey={SEARCH_QUERY_KEY}
        action={{
          label: t("users.invite"),
          to: "invite",
        }}
        emptyState={{
          empty: {
            heading: t("users.list.empty.heading"),
            description: t("users.list.empty.description"),
          },
          filtered: {
            heading: t("users.list.filtered.heading"),
            description: t("users.list.filtered.description"),
          },
        }}
      />
    </Container>
  )
}

const columnHelper = createDataTableColumnHelper<UserResponse>()
const userStatusMap = reduceOptionsToMap(userStatusOptions)
const teamRolesMap = reduceOptionsToMap(teamRoleOptions)
const systemRolesMap = reduceOptionsToMap(systemRoleOptions)

const useColumns = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  // const prompt = usePrompt()

  // const deleteMutation = safQuery.useMutation("delete", "/api/admin/users/{userId}")

  // const handleDelete = async () => {
  //   const confirm = await prompt({
  //     title: t("general.areYouSure"),
  //     description: t("users.cancelInviteWarning", {
  //       email: invite.email,
  //     }),
  //     cancelText: t("actions.cancel"),
  //     confirmText: t("actions.delete"),
  //   })

  //   if (!confirm) {
  //     return
  //   }

  //   try {
  //     if (teamId != null) {
  //       await cancelTeamInvite.mutateAsync({
  //         params: {
  //           path: {
  //             teamId: parseInt(teamId),
  //             invitationId: invite.id,
  //           },
  //         },
  //       })
  //       queryClient.invalidateQueries(
  //         safQuery.queryOptions("get", "/api/admin/teams/{teamId}/invitations", {
  //           params: {
  //             path: {
  //               teamId: parseInt(teamId),
  //             },
  //           },
  //         }),
  //       )
  //     } else {
  //       await deleteMutation.mutateAsync({
  //         params: {
  //           path: {
  //             invitationId: invite.id,
  //           },
  //         },
  //       })
  //       queryClient.invalidateQueries(safQuery.queryOptions("get", "/api/admin/invitations"))
  //     }
  //     toast.success(t("users.inviteCancelled"))
  //   } catch (error) {
  //     toast.error((error as any)?.message || t("users.cancelFailed"))
  //   }
  // }

  return useMemo(
    () => [
      columnHelper.accessor("email", {
        header: t("fields.email"),
        cell: ({ row }) => {
          return row.original.email
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc"),
      }),
      columnHelper.accessor("name", {
        header: t("fields.name"),
        cell: ({ row }) => {
          return row.original.name || "-"
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc"),
      }),
      columnHelper.accessor("systemRoles", {
        header: "System Roles",
        cell: ({ getValue }) => (
          <div className="flex items-center gap-2">
            {getValue()?.map((role) => <Badge size="2xsmall">{systemRolesMap[role].label}</Badge>)}
          </div>
        ),
        maxSize: 250,
        minSize: 200,
      }),
      columnHelper.accessor("teams", {
        header: "Teams",
        cell: ({ getValue }) => (
          <div className="flex flex-nowrap items-center gap-2 rounded-md border border-ui-border-base bg-ui-bg-component py-0.5 pe-0.5 ps-2">
            {getValue()?.map((team) => (
              <div className="flex flex-nowrap items-center gap-1.5" key={team.id}>
                <Text size="xsmall">{team.name}</Text>
                {team.roles.map((role) => (
                  <Badge size="2xsmall">{teamRolesMap[role].label}</Badge>
                ))}
              </div>
            ))}
          </div>
        ),
        maxSize: 250,
        minSize: 200,
      }),
      columnHelper.accessor("userStatus", {
        header: () => t("fields.status"),
        cell: ({ getValue }) => {
          const value = getValue()
          return (
            <DataTableStatusCell
              color={
                {
                  active: "green",
                  inactive: "gray",
                  suspended: "yellow",
                  deleted: "red",
                  _: "gray",
                }[value || "_"] as DataTableStatusCellProps["color"]
              }
            >
              {userStatusMap[value]?.label || t("general.unknown")}
            </DataTableStatusCell>
          )
        },
      }),
      columnHelper.action({
        actions: [
          [
            {
              icon: <PencilSquare />,
              onClick: (ctx) => {
                navigate(`${ctx.row.original.id}/edit`)
              },
            },
            // {
            //   icon: <Trash />,
            //   onClick: () =>
            //     deleteMutation({
            //       miniApp: ctx.row.original,
            //     }),
            // },
          ],
        ],
      }),
    ],
    [t, navigate],
  )
}
