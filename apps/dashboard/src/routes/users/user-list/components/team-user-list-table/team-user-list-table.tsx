import { Badge, Container, createDataTableColumnHelper } from "@saf/ui"
import { keepPreviousData } from "@tanstack/react-query"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate, useParams } from "react-router-dom"

import { safQuery } from "@/client"
import { DataTable, emptyDataTableValue } from "@/components/data-table"
import {
  DataTableStatusCell,
  DataTableStatusCellProps,
} from "@/components/data-table/components/data-table-status-cell/data-table-status-cell"
import { teamRoleOptions, userStatusOptions } from "@/configs"
import { usePaginationQueryParam } from "@/hooks/use-pagination-query-params"
import { reduceOptionsToMap } from "@/lib/utils"
import { PencilSquare } from "@medusajs/icons"
import { components } from "@saf/sdk"

type TeamUserResponse = components["schemas"]["TeamUserResponse"]

const PAGE_SIZE = 20
const SEARCH_QUERY_KEY = "email"

export const TeamUserListTable = () => {
  const { t } = useTranslation()
  const queryParams = usePaginationQueryParam([SEARCH_QUERY_KEY])
  const { teamId = "" } = useParams()

  const { data, isError, error, isPending } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/users",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
        },
        query: queryParams,
      },
    },
    {
      placeholderData: keepPreviousData,
    },
  )

  const columns = useColumns()

  if (isError) {
    throw error
  }

  return (
    <Container className="divide-y p-0">
      <DataTable
        data={data?.items || emptyDataTableValue}
        columns={columns}
        getRowId={(row) => `${row.id}`}
        rowCount={data?.totalCount}
        rowHref={(row) => `${row.id}`}
        pageSize={PAGE_SIZE}
        isLoading={isPending}
        searchQueryKey={SEARCH_QUERY_KEY}
        heading={t("users.domain")}
        action={{
          label: t("users.invite"),
          to: "invite",
        }}
        emptyState={{
          empty: {
            heading: t("users.list.empty.heading"),
            description: t("users.list.empty.description"),
          },
          filtered: {
            heading: t("users.list.filtered.heading"),
            description: t("users.list.filtered.description"),
          },
        }}
      />
    </Container>
  )
}

const columnHelper = createDataTableColumnHelper<TeamUserResponse>()
const userStatusMap = reduceOptionsToMap(userStatusOptions)
const teamRolesMap = reduceOptionsToMap(teamRoleOptions)

const useColumns = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()

  return useMemo(
    () => [
      columnHelper.accessor("email", {
        header: t("fields.email"),
        cell: ({ getValue }) => {
          return getValue()
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc"),
      }),
      columnHelper.accessor("name", {
        header: t("fields.name"),
        cell: ({ getValue }) => {
          return getValue() || "-"
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc"),
      }),
      columnHelper.accessor("teamRoles", {
        header: "Roles",
        cell: ({ getValue }) => (
          <div className="flex items-center gap-2">
            {getValue()?.map((role) => <Badge size="2xsmall">{teamRolesMap[role].label}</Badge>)}
          </div>
        ),
        maxSize: 250,
        minSize: 200,
      }),
      columnHelper.accessor("userStatus", {
        header: () => t("fields.status"),
        cell: ({ getValue }) => {
          const value = getValue()
          return (
            <DataTableStatusCell
              color={
                {
                  active: "green",
                  inactive: "gray",
                  suspended: "yellow",
                  deleted: "red",
                  _: "gray",
                }[value || "_"] as DataTableStatusCellProps["color"]
              }
            >
              {userStatusMap[value]?.label || t("general.unknown")}
            </DataTableStatusCell>
          )
        },
      }),
      columnHelper.action({
        actions: [
          [
            {
              icon: <PencilSquare />,
              onClick: (ctx) => {
                navigate(`${ctx.row.original.id}/edit`)
              },
            },
          ],
        ],
      }),
    ],
    [t, navigate],
  )
}
