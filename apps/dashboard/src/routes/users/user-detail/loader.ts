import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { LoaderFunctionArgs } from "react-router-dom"

export const userLoader = async ({ params }: LoaderFunctionArgs) => {
  const { userId, teamId } = params || {}

  const teamUserQueryOption = safQuery.queryOptions("get", "/api/admin/teams/{teamId}/users/{userId}", {
    params: {
      path: {
        teamId: parseInt(teamId || ""),
        userId: parseInt(userId || ""),
      },
    },
  })

  const userQueryOption = safQuery.queryOptions("get", "/api/admin/users/{userId}", {
    params: {
      path: {
        userId: parseInt(userId || ""),
      },
    },
  })

  return teamId != null
    ? queryClient.ensureQueryData(teamUserQueryOption)
    : queryClient.ensureQueryData(userQueryOption)
}
