import { useLoaderData, useParams } from "react-router-dom"

import { UserGeneralSection } from "./components/user-general-section"
import { userLoader } from "./loader"

import { safQuery } from "@/client"
import { SingleColumnPageSkeleton } from "../../../components/common/skeleton"
import { SingleColumnPage } from "../../../components/layout/pages"

export const UserDetail = () => {
  const initialData = useLoaderData() as Awaited<ReturnType<typeof userLoader>>

  const { userId, teamId } = useParams() || {}

  const teamUserQuery = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/users/{userId}",
    {
      params: {
        path: {
          teamId: parseInt(teamId || ""),
          userId: parseInt(userId || ""),
        },
      },
    },
    {
      initialData: initialData,
      enabled: userId != null && teamId != null,
    },
  )

  const userQuery = safQuery.useQuery(
    "get",
    "/api/admin/users/{userId}",
    {
      params: {
        path: {
          userId: parseInt(userId || ""),
        },
      },
    },
    {
      initialData: initialData,
      enabled: userId != null,
    },
  )

  const { data: user, isLoading, isError, error } = teamId != null ? teamUserQuery : userQuery

  if (isLoading || !user) {
    return <SingleColumnPageSkeleton sections={1} showJSON showMetadata />
  }

  if (isError) {
    throw error
  }

  return (
    <SingleColumnPage data={user}>
      <UserGeneralSection user={user} />
    </SingleColumnPage>
  )
}
