import { safQuery } from "@/client"
import { SectionRow } from "@/components/common/section"
import { systemRoleOptions, teamRoleOptions, userStatusOptions } from "@/configs"
import { useDate } from "@/hooks/use-date"
import { reduceOptionsToMap } from "@/lib/utils"
import { isTeamUser, isUser, TeamUser, User } from "@/routes/users/types"
import { Badge, Button, Container, Heading, StatusBadge, Text, toast, usePrompt } from "@saf/ui"
import { StatusBadgeProps } from "@ui/components/status-badge"
import { useTranslation } from "react-i18next"
import { Link, useNavigate, useParams } from "react-router-dom"

type UserGeneralSectionProps = {
  user: User | TeamUser
}

const userStatusMap = reduceOptionsToMap(userStatusOptions)
const systemRolesMap = reduceOptionsToMap(systemRoleOptions)
const teamRolesMap = reduceOptionsToMap(teamRoleOptions)

export const UserGeneralSection = ({ user }: UserGeneralSectionProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const prompt = usePrompt()
  const { teamId } = useParams()
  const { getFullDate } = useDate()

  const { mutateAsync: deleteTeamUser } = safQuery.useMutation("delete", "/api/admin/teams/{teamId}/users/{userId}")

  const { mutateAsync: deleteUser } = safQuery.useMutation("delete", "/api/admin/users/{userId}")

  const name = user.name || ""

  const handleDeleteUser = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("users.deleteUserWarning", {
        name: name ?? user.email,
      }),
      verificationText: name ?? user.email,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel"),
    })

    if (!res) {
      return
    }

    if (teamId != null) {
      await deleteTeamUser(
        {
          params: {
            path: {
              teamId: parseInt(teamId || ""),
              userId: user.id,
            },
          },
        },
        {
          onSuccess: () => {
            toast.success(t("users.deleteUserSuccess", { name: user.email }))
            navigate("..")
          },
          onError: (error) => {
            toast.error(error.message)
          },
        },
      )
    } else {
      await deleteUser(
        {
          params: {
            path: {
              userId: user.id,
            },
          },
        },
        {
          onSuccess: () => {
            toast.success(t("users.deleteUserSuccess", { name: user.email }))
            navigate("..")
          },
          onError: (error) => {
            toast.error(error.message)
          },
        },
      )
    }
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>{user.email}</Heading>
        <div className="flex shrink-0 items-center gap-x-2">
          <Button size="small" variant="secondary" asChild>
            <Link to="edit">{t("edit", "Edit")}</Link>
          </Button>
          <Button size="small" variant="danger" onClick={handleDeleteUser}>
            {t("delete", "Delete")}
          </Button>
        </div>
      </div>
      <div className="divide-y">
        <SectionRow title={t("fields.email")} value={user.email} />
        <SectionRow title={t("fields.name")} value={user.name || "-"} />
        <SectionRow
          title={t("fields.status")}
          value={
            <StatusBadge
              color={
                {
                  active: "green",
                  inactive: "gray",
                  suspended: "yellow",
                  deleted: "red",
                  _: "gray",
                }[user.userStatus || "_"] as StatusBadgeProps["color"]
              }
            >
              {userStatusMap[user.userStatus]?.label || t("fields.status.unknown")}
            </StatusBadge>
          }
        />
        {...isUser(user)
          ? [
              <SectionRow
                title={t("fields.systemRoles")}
                value={
                  <div className="flex items-center gap-2">
                    {user.systemRoles?.map((role) => <Badge size="2xsmall">{systemRolesMap[role].label}</Badge>)}
                  </div>
                }
              />,
              <SectionRow
                title={t("fields.teamRoles")}
                value={
                  <div className="flex flex-nowrap items-center gap-2 rounded-md border border-ui-border-base bg-ui-bg-component py-0.5 pe-0.5 ps-2">
                    {user.teams?.map((team) => (
                      <div className="flex flex-nowrap items-center gap-1.5" key={team.id}>
                        <Text size="xsmall">{team.name}</Text>
                        {team.roles.map((role) => (
                          <Badge size="2xsmall">{teamRolesMap[role].label}</Badge>
                        ))}
                      </div>
                    ))}
                  </div>
                }
              />,
            ]
          : []}
        {isTeamUser(user) ? (
          <SectionRow
            title={t("fields.teamRoles")}
            value={
              <div className="flex items-center gap-2">
                {user.teamRoles?.map((role) => <Badge size="2xsmall">{teamRolesMap[role].label}</Badge>)}
              </div>
            }
          />
        ) : null}
        <SectionRow title="Created At" value={getFullDate({ date: user.createdAt })} />
      </div>
    </Container>
  )
}
