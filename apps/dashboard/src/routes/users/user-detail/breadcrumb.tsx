import { safQuery } from "@/client"
import { components } from "@saf/sdk"
import { UIMatch } from "react-router-dom"

type UserDetailBreadcrumbProps = UIMatch<components["schemas"]["UserResponse"]>

export const UserDetailBreadcrumb = (props: UserDetailBreadcrumbProps) => {
  const { userId, teamId } = props.params || {}

  const teamUserQuery = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/users/{userId}",
    {
      params: {
        path: {
          teamId: parseInt(teamId || ""),
          userId: parseInt(userId || ""),
        },
      },
    },
    {
      initialData: props.data,
      enabled: userId != null && teamId != null,
    },
  )

  const userQuery = safQuery.useQuery(
    "get",
    "/api/admin/users/{userId}",
    {
      params: {
        path: {
          userId: parseInt(userId || ""),
        },
      },
    },
    {
      initialData: props.data,
      enabled: userId != null,
    },
  )

  const { data: user } = teamId != null ? teamUserQuery : userQuery

  if (!user) {
    return null
  }

  const name = user.name || ""

  const display = name || user.email

  return <span>{display}</span>
}
