import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { Button, Input } from "@saf/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import * as zod from "zod"

import { safQuery } from "@/client"
import { Form } from "@/components/common/form"
import { KeyboundForm } from "@/components/common/keybound-form"
import { RouteDrawer, useRouteModal } from "@/components/modals"
import { components } from "@saf/sdk"

type EditUserFormProps = {
  user: components["schemas"]["UserResponse"]
}

const EditUserFormSchema = zod.object({
  name: zod.string().optional(),
})

export const EditUserForm = ({ user }: EditUserFormProps) => {
  const { t } = useTranslation()
  const { handleSuccess } = useRouteModal()

  const form = useForm<zod.infer<typeof EditUserFormSchema>>({
    defaultValues: {
      name: user.name || "",
    },
    resolver: zod<PERSON>esolver(EditUserFormSchema),
  })

  const { mutateAsync, isPending } = safQuery.useMutation("patch", "/api/admin/users/{userId}")

  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      {
        params: {
          path: {
            userId: user.id,
          },
        },
        body: values,
      },
      {
        onSuccess: () => {
          handleSuccess()
        },
      },
    )
  })

  return (
    <RouteDrawer.Form form={form}>
      <KeyboundForm onSubmit={handleSubmit} className="flex flex-1 flex-col overflow-hidden">
        <RouteDrawer.Body className="flex max-w-full flex-1 flex-col gap-y-8 overflow-y-auto">
          <Form.Field
            control={form.control}
            name="name"
            render={({ field }) => {
              return (
                <Form.Item>
                  <Form.Label>{t("fields.firstName")}</Form.Label>
                  <Form.Control>
                    <Input {...field} />
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
        </RouteDrawer.Body>
        <RouteDrawer.Footer>
          <div className="flex items-center justify-end gap-x-2">
            <RouteDrawer.Close asChild>
              <Button size="small" variant="secondary">
                {t("actions.cancel")}
              </Button>
            </RouteDrawer.Close>
            <Button size="small" type="submit" isLoading={isPending}>
              {t("actions.save")}
            </Button>
          </div>
        </RouteDrawer.Footer>
      </KeyboundForm>
    </RouteDrawer.Form>
  )
}
