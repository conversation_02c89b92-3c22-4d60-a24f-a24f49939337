import { safQuery } from "@/client"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button, Input, toast } from "@saf/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"

import { queryClient } from "@/client/react-query"
import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { KeyboundForm } from "@/components/common/keybound-form"
import { Combobox } from "@/components/inputs/combobox"
import { RouteDrawer, useRouteModal } from "@/components/modals"
import { teamRoleOptions } from "@/configs"
import { i18n } from "@/i18n/i18n"
import { createEnumFromOptions } from "@/lib/conversion"
import { components } from "@saf/sdk"
import { useParams } from "react-router-dom"
import { z } from "zod"

type EditUserFormProps = {
  user: components["schemas"]["TeamUserResponse"]
}

const EditTeamUserFormSchema = z.object({
  teamRoles: z.array(z.enum(createEnumFromOptions(teamRoleOptions))).nonempty(i18n.t("users.validation.roles")),
})

export const EditTeamUserForm = ({ user }: EditUserFormProps) => {
  const { t } = useTranslation()
  const { handleSuccess } = useRouteModal()
  const { teamId = "" } = useParams()

  const form = useForm<z.infer<typeof EditTeamUserFormSchema>>({
    defaultValues: {
      teamRoles: user.teamRoles || [],
    },
    resolver: zodResolver(EditTeamUserFormSchema),
  })

  const { mutateAsync, isPending } = safQuery.useMutation("patch", "/api/admin/teams/{teamId}/users/{userId}")

  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      {
        params: {
          path: {
            teamId: parseInt(teamId),
            userId: user.id,
          },
        },
        body: {
          teamRoles: values.teamRoles,
        },
      },
      {
        onSuccess: () => {
          toast.success(t("operationFeedback.updateSuccess"))
          queryClient.invalidateQueries(
            safQuery.queryOptions("get", "/api/admin/teams/{teamId}/users", {
              params: {
                path: {
                  teamId: parseInt(teamId),
                },
              },
            }),
          )
          queryClient.invalidateQueries(
            safQuery.queryOptions("get", "/api/admin/teams/{teamId}/users/{userId}", {
              params: {
                path: {
                  teamId: parseInt(teamId),
                  userId: user.id,
                },
              },
            }),
          )
          handleSuccess()
        },
      },
    )
  })

  return (
    <RouteDrawer.Form form={form}>
      <KeyboundForm onSubmit={handleSubmit} className="flex flex-1 flex-col overflow-hidden">
        <RouteDrawer.Body className="flex max-w-full flex-1 flex-col gap-y-8 overflow-y-auto">
          <Form.Label className="w-full space-y-2">
            <span>{t("fields.name")}</span>
            <Input disabled value={user.name} />
          </Form.Label>
          <Form.Label className="w-full space-y-2">
            <span>{t("fields.email")}</span>
            <Input disabled value={user.email} />
          </Form.Label>
          <Field {...form} name="teamRoles" label={t("fields.teamRoles")}>
            <Combobox options={teamRoleOptions} />
          </Field>
        </RouteDrawer.Body>
        <RouteDrawer.Footer>
          <div className="flex items-center justify-end gap-x-2">
            <RouteDrawer.Close asChild>
              <Button size="small" variant="secondary">
                {t("actions.cancel")}
              </Button>
            </RouteDrawer.Close>
            <Button size="small" type="submit" isLoading={isPending}>
              {t("actions.save")}
            </Button>
          </div>
        </RouteDrawer.Footer>
      </KeyboundForm>
    </RouteDrawer.Form>
  )
}
