import { safQuery } from "@/client"
import { RouteFocusModalError } from "@/components/common/error-result"
import { ModalFormSectionSkeleton } from "@/components/common/skeleton"
import { RouteDrawer } from "@/components/modals"
import { Heading } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { isTeamUser } from "../types"
import { EditTeamUserForm } from "./components/edit-team-user-form"
import { EditUserForm } from "./components/edit-user-form"

export const UserEdit = () => {
  const { t } = useTranslation()
  const { teamId, userId = "" } = useParams()

  const teamUserQuery = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/users/{userId}",
    {
      params: {
        path: {
          teamId: parseInt(teamId || ""),
          userId: parseInt(userId),
        },
      },
    },
    {
      enabled: teamId != null,
    },
  )

  const userQuery = safQuery.useQuery(
    "get",
    "/api/admin/users/{userId}",
    {
      params: {
        path: {
          userId: parseInt(userId || ""),
        },
      },
    },
    {
      enabled: teamId == null,
    },
  )

  const { data, isLoading, error } = teamId != null ? teamUserQuery : userQuery

  return (
    <RouteDrawer>
      <RouteDrawer.Header>
        <Heading>{t("users.editUser")}</Heading>
      </RouteDrawer.Header>
      {isLoading ? (
        <ModalFormSectionSkeleton fieldCount={5} />
      ) : error || !data ? (
        <RouteFocusModalError message={error?.message} />
      ) : isTeamUser(data) ? (
        <EditTeamUserForm user={data} />
      ) : (
        <EditUserForm user={data} />
      )}
    </RouteDrawer>
  )
}
