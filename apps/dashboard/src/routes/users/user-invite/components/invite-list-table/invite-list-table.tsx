import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { ActionMenu } from "@/components/common/action-menu"
import { DataTable, emptyDataTableValue } from "@/components/data-table"
import { systemRoleOptions, teamRoleOptions } from "@/configs"
import { useDate } from "@/hooks/use-date"
import { usePaginationQueryParam } from "@/hooks/use-pagination-query-params"
import { reduceOptionsToMap } from "@/lib/utils"
import { Link, Trash } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { Badge, Container, StatusBadge, Text, toast, Tooltip, usePrompt } from "@saf/ui"
import { keepPreviousData } from "@tanstack/react-query"
import { createColumnHelper } from "@tanstack/react-table"
import copy from "copy-to-clipboard"
import { format } from "date-fns"
import { useMemo } from "react"
import { Trans, useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"

const PAGE_SIZE = 10
const SEARCH_KEY = "email"
const INVITE_URL = `${window.location.origin}/invite?token=`
type AdminInvite = components["schemas"]["InvitationResponse"]

export const InviteListTable = () => {
  const { t } = useTranslation()
  const { teamId } = useParams()
  const queryParams = usePaginationQueryParam([SEARCH_KEY])

  const systemInvites = safQuery.useQuery(
    "get",
    "/api/admin/invitations",
    {
      params: {
        query: {
          ...queryParams,
        },
      },
    },
    {
      enabled: !teamId,
      placeholderData: keepPreviousData,
    },
  )

  const teamInvites = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/invitations",
    {
      params: {
        path: {
          teamId: parseInt(teamId || ""),
        },
        query: {
          ...queryParams,
        },
      },
    },
    {
      enabled: !!teamId,
      placeholderData: keepPreviousData,
    },
  )

  const { data: invites, isLoading, isError, error } = teamId ? teamInvites : systemInvites

  const columns = useColumns()

  if (isError) {
    throw error
  }

  return (
    <Container className="overflow-hidden p-0">
      <DataTable
        data={invites?.items || emptyDataTableValue}
        columns={columns}
        searchQueryKey={SEARCH_KEY}
        getRowId={(row) => `${row.id}`}
        rowCount={invites?.totalCount}
        pageSize={PAGE_SIZE}
        heading={t("users.invites")}
        isLoading={isLoading}
      />
    </Container>
  )
}

const InviteActions = ({ invite }: { invite: AdminInvite }) => {
  const { teamId } = useParams()

  const cancelTeamInvite = safQuery.useMutation("delete", "/api/admin/teams/{teamId}/invitations/{invitationId}/cancel")
  const cancelSystemInvite = safQuery.useMutation("delete", "/api/admin/invitations/{invitationId}/cancel")

  const prompt = usePrompt()
  const { t } = useTranslation()

  const handleCancel = async () => {
    const confirm = await prompt({
      title: t("general.areYouSure"),
      description: t("users.cancelInviteWarning", {
        email: invite.email,
      }),
      cancelText: t("actions.cancel"),
      confirmText: t("actions.delete"),
    })

    if (!confirm) {
      return
    }

    try {
      if (teamId != null) {
        await cancelTeamInvite.mutateAsync({
          params: {
            path: {
              teamId: parseInt(teamId),
              invitationId: invite.id,
            },
          },
        })
        queryClient.invalidateQueries(
          safQuery.queryOptions("get", "/api/admin/teams/{teamId}/invitations", {
            params: {
              path: {
                teamId: parseInt(teamId),
              },
            },
          }),
        )
      } else {
        await cancelSystemInvite.mutateAsync({
          params: {
            path: {
              invitationId: invite.id,
            },
          },
        })
        queryClient.invalidateQueries(safQuery.queryOptions("get", "/api/admin/invitations"))
      }
      toast.success(t("users.inviteCancelled"))
    } catch (error) {
      toast.error((error as any)?.message || t("users.cancelFailed"))
    }
  }

  const handleCopyInviteLink = () => {
    const inviteUrl = `${INVITE_URL}${invite.token}`
    copy(inviteUrl)
  }

  return (
    <ActionMenu
      groups={[
        {
          actions: [
            {
              icon: <Link />,
              label: t("users.copyInviteLink"),
              onClick: handleCopyInviteLink,
            },
          ],
        },
        {
          actions: [
            {
              icon: <Trash />,
              label: t("actions.cancel"),
              onClick: handleCancel,
            },
          ],
        },
      ]}
    />
  )
}

const columnHelper = createColumnHelper<components["schemas"]["InvitationResponse"]>()

const useColumns = () => {
  const { teamId } = useParams()
  const { t } = useTranslation()
  const { getFullDate } = useDate()
  const teamRolesMap = reduceOptionsToMap(teamRoleOptions)
  const systemRolesMap = reduceOptionsToMap(systemRoleOptions)

  return useMemo(
    () => [
      columnHelper.accessor("email", {
        header: t("fields.email"),
        cell: ({ getValue }) => {
          return getValue()
        },
      }),
      ...(teamId == null
        ? [
            columnHelper.accessor("systemRoles", {
              header: "System Roles",
              cell: ({ getValue }) =>
                getValue()?.map((role) => <Badge size="2xsmall">{systemRolesMap[role].label}</Badge>),
            }),
          ]
        : []),
      columnHelper.accessor("teamAssignments", {
        header: "Teams",
        cell: ({ getValue }) => (
          <div className="m-1 flex items-center gap-x-2 gap-y-1 rounded-md border border-ui-border-base bg-ui-bg-component py-1 pe-0.5 ps-2">
            {getValue()?.map((assignment) => (
              <div className="flex flex-wrap items-center gap-1" key={assignment.teamId}>
                <Text size="xsmall">{assignment.teamName}</Text>
                {assignment.teamRoles.map((role) => (
                  <Badge size="2xsmall">{teamRolesMap[role].label}</Badge>
                ))}
              </div>
            ))}
          </div>
        ),
        maxSize: 250,
        minSize: 200,
      }),
      columnHelper.accessor("invitedBy", {
        header: "Invited By",
        cell: ({ row }) => <Text size="xsmall">{row.original.invitedBy?.name || "Unknown"}</Text>,
      }),
      columnHelper.accessor("createdAt", {
        header: "Invited at",
        cell: ({ row }) => {
          return (
            <Tooltip
              content={getFullDate({
                date: row.original.createdAt,
                includeTime: true,
              })}
            >
              <Text size="xsmall">{getFullDate({ date: row.original.createdAt })}</Text>
            </Tooltip>
          )
        },
      }),
      columnHelper.accessor("invitationStatus", {
        header: t("fields.status"),
        cell: ({ getValue, row }) => {
          const expired = new Date(row.original.expiredAt) < new Date()

          if (getValue() === "accepted") {
            return <StatusBadge color="green">{t("users.inviteStatus.accepted")}</StatusBadge>
          }

          if (getValue() === "cancelled") {
            return <StatusBadge color="grey">{t("users.inviteStatus.cancelled")}</StatusBadge>
          }

          if (expired) {
            return (
              <Tooltip
                content={t("users.expiredOnDate", {
                  date: format(new Date(row.original.expiredAt), "dd MMM, yyyy"),
                })}
              >
                <StatusBadge color="red">{t("users.inviteStatus.expired")}</StatusBadge>
              </Tooltip>
            )
          }

          return (
            <Tooltip
              content={
                <Trans
                  i18nKey={"users.validFromUntil"}
                  components={[
                    <span key="from" className="font-medium" />,
                    <span key="untill" className="font-medium" />,
                  ]}
                  values={{
                    from: format(new Date(row.original.createdAt), "dd MMM, yyyy"),
                    until: format(new Date(row.original.expiredAt), "dd MMM, yyyy"),
                  }}
                />
              }
            >
              <StatusBadge color="orange">{t("users.inviteStatus.pending")}</StatusBadge>
            </Tooltip>
          )
        },
      }),
      columnHelper.display({
        id: "action",
        cell: ({ row }) => <InviteActions invite={row.original} />,
      }),
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [getFullDate, t, teamId],
  )
}
