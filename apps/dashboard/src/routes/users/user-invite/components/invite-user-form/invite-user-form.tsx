import { RouteFocusModal } from "@/components/modals/index.ts"
import { Heading } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { InviteListTable } from "../invite-list-table/invite-list-table"
import { InviteToSystemForm } from "./invite-to-system-form"
import { InviteToTeamForm } from "./invite-to-team-form"

export const InviteUserForm = () => {
  const { t } = useTranslation()
  const { teamId } = useParams()

  return (
    <RouteFocusModal>
      <RouteFocusModal.Header />
      <RouteFocusModal.Body className="flex h-full flex-col overflow-hidden">
        <div className="flex flex-1 flex-col items-center overflow-y-auto">
          <div className="flex w-full max-w-5xl flex-col gap-y-8 px-2 py-16">
            <Heading>{teamId != null ? t("users.inviteUserToTeam") : t("users.inviteUserToSystem")}</Heading>
            {teamId != null ? <InviteToTeamForm /> : <InviteToSystemForm />}
            <InviteListTable />
          </div>
        </div>
      </RouteFocusModal.Body>
    </RouteFocusModal>
  )
}
