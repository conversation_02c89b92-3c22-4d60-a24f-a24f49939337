import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Combobox } from "@/components/inputs/combobox"
import { teamRoleOptions } from "@/configs"
import { i18n } from "@/i18n/i18n"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { Alert, Button, Input, toast } from "@saf/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { z } from "zod"

const inviteToTeamFormSchema = z.object({
  email: z.string().email(),
  roles: z.array(z.enum(createEnumFromOptions(teamRoleOptions))).nonempty(i18n.t("users.validation.roles")),
})

export const InviteToTeamForm = () => {
  const { t } = useTranslation()
  const { teamId = "" } = useParams()

  const form = useForm<z.infer<typeof inviteToTeamFormSchema>>({
    resolver: zodResolver(inviteToTeamFormSchema),
    defaultValues: {
      email: "",
      roles: [],
    },
  })

  const { mutateAsync, isPending } = safQuery.useMutation("post", "/api/admin/teams/{teamId}/invitations")

  const handleSubmit = form.handleSubmit(async (values) => {
    try {
      await mutateAsync({
        params: {
          path: {
            teamId: parseInt(teamId),
          },
        },
        body: values,
      })
      form.reset()
      toast.success(t("users.inviteSent"))
      queryClient.invalidateQueries(
        safQuery.queryOptions("get", "/api/admin/teams/{teamId}/invitations", {
          params: {
            path: {
              teamId: parseInt(teamId),
            },
          },
        }),
      )
    } catch (error) {
      form.setError("root", {
        type: "manual",
        message: (error as any)?.message,
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="flex flex-col gap-y-4">
        {form.formState.errors.root && (
          <Alert variant="error" dismissible={false} className="text-balance">
            {form.formState.errors.root.message}
          </Alert>
        )}

        <div className="flex flex-col gap-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Field {...form} name="email" label={t("fields.email")}>
              <Input type="email" />
            </Field>
            <Field {...form} name="roles" label={t("fields.teamRoles")}>
              <Combobox options={teamRoleOptions} />
            </Field>
          </div>
          <div className="flex items-center justify-end">
            <Button size="small" type="submit" isLoading={isPending}>
              {t("users.sendInvite")}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  )
}
