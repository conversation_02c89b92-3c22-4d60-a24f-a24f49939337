import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Combobox } from "@/components/inputs/combobox"
import { systemRoleOptions } from "@/configs"
import { i18n } from "@/i18n/i18n"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { Alert, Button, Input, toast } from "@saf/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import { z } from "zod"

const inviteToSystemFormSchema = z.object({
  email: z.string().email(),
  systemRoles: z.array(z.enum(createEnumFromOptions(systemRoleOptions))).nonempty(i18n.t("users.validation.roles")),
})

export const InviteToSystemForm = () => {
  const { t } = useTranslation()

  const form = useForm<z.infer<typeof inviteToSystemFormSchema>>({
    resolver: zodResolver(inviteToSystemFormSchema),
    defaultValues: {
      email: "",
      systemRoles: [],
    },
  })

  const { mutateAsync, isPending } = safQuery.useMutation("post", "/api/admin/invitations")

  const handleSubmit = form.handleSubmit(async (values) => {
    try {
      await mutateAsync({
        body: values,
      })
      form.reset()
      toast.success(t("users.inviteSent"))
      queryClient.invalidateQueries(safQuery.queryOptions("get", "/api/admin/invitations"))
    } catch (error) {
      form.setError("root", {
        type: "manual",
        message: (error as any)?.message,
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="flex flex-col gap-y-4">
        {form.formState.errors.root && (
          <Alert variant="error" dismissible={false} className="text-balance">
            {form.formState.errors.root.message}
          </Alert>
        )}

        <div className="flex flex-col gap-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Field {...form} name="email" label={t("fields.email")}>
              <Input type="email" />
            </Field>
            <Field {...form} name="systemRoles" label={t("fields.systemRoles")}>
              <Combobox options={systemRoleOptions} />
            </Field>
          </div>
          <div className="flex items-center justify-end">
            <Button size="small" type="submit" isLoading={isPending}>
              {t("users.sendInvite")}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  )
}
