import { LogoBox } from "@/components/common/logo-box"
import { useSession } from "@/hooks/auth"
import { isNumeric } from "@/lib/utils"
import { IconLoader } from "@tabler/icons-react"
import { Navigate, Outlet, useParams } from "react-router-dom"

export const TEAM_ID_STORAGE_KEY = "teamId"

export const TeamRedirect = () => {
  const { teamId: urlTeamId } = useParams()
  const { data: user, isLoading, error } = useSession()

  if (isLoading || user == null) {
    return (
      <div className="flex min-h-dvh w-dvw flex-col items-center justify-center gap-4 bg-ui-bg-subtle">
        <LogoBox />
        <div className="flex items-center gap-2">
          <IconLoader className="size-6 animate-spin text-ui-fg-interactive" />
          Loading teams...
        </div>
      </div>
    )
  }

  if (error) {
    throw error
  }

  const userTeamIds = user?.teams?.map((team) => team.id) || []

  const lastVisitedTeamId = localStorage.getItem(TEAM_ID_STORAGE_KEY) || null

  if (urlTeamId != null && isNumeric(urlTeamId) && userTeamIds.includes(parseInt(urlTeamId))) {
    return <Outlet />
  } else if (urlTeamId != null && !userTeamIds.includes(parseInt(urlTeamId))) {
    return <Navigate to="/team-not-found" replace />
    // TODO: Revise this logic since user can enter any team id in the url
  } else if (isNumeric(lastVisitedTeamId) && userTeamIds.includes(parseInt(lastVisitedTeamId))) {
    return <Navigate to={`/teams/${lastVisitedTeamId}`} replace />
  } else if (userTeamIds.length > 0) {
    const firstTeamId = userTeamIds[0]
    localStorage.setItem(TEAM_ID_STORAGE_KEY, firstTeamId.toString())
    return <Navigate to={`/teams/${firstTeamId}`} replace />
  }

  return <Navigate to="/team-not-found" replace />
}
