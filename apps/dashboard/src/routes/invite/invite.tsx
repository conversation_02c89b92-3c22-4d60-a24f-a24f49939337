import { safQ<PERSON>y } from "@/client"
import { i18n } from "@/i18n/i18n"
import { zodResolver } from "@hookform/resolvers/zod"
import { Spin<PERSON> } from "@medusajs/icons"
import { <PERSON><PERSON>, <PERSON><PERSON>, Heading, Hint, Input, Text, toast } from "@saf/ui"
import { AnimatePresence, motion } from "motion/react"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import { Link, useSearchParams } from "react-router-dom"
import * as z from "zod"
import { Form } from "../../components/common/form"
import AvatarBox from "../../components/common/logo-box/avatar-box"

export const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
  .regex(/[a-z]/, "Password must contain at least one lowercase letter")
  .regex(/[0-9]/, "Password must contain at least one number")
  .regex(/[^A-Za-z0-9]/, "Password must contain at least one special character")

export const nameSchema = z
  .string()
  .min(2, "Name must be at least 2 characters")
  .max(50, "Name must be less than 50 characters")

export const acceptInvitationSchema = z
  .object({
    name: nameSchema,
    password: passwordSchema,
    repeatPassword: z.string().min(1),
  })
  .superRefine(({ password, repeatPassword }, ctx) => {
    if (password !== repeatPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: i18n.t("invite.passwordMismatch"),
        path: ["repeatPassword"],
      })
    }
  })

export type AcceptInvitationSchema = z.infer<typeof acceptInvitationSchema>

export const Invite = () => {
  const [searchParams] = useSearchParams()
  const [success, setSuccess] = useState(false)

  const token = searchParams.get("token") || ""

  const { isLoading, isError, error } = safQuery.useQuery(
    "get",
    "/api/admin/invitations/{token}",
    {
      params: {
        path: {
          token,
        },
      },
    },
    {
      enabled: !!token,
    },
  )

  return (
    <div className="relative flex min-h-dvh w-dvw items-center justify-center bg-ui-bg-subtle p-4">
      <div className="flex w-full max-w-[360px] flex-col items-center">
        <AvatarBox checked={success} />
        <div className="flex max-h-[557px] w-full items-center justify-center will-change-contents">
          {isLoading ? (
            <Spinner className="animate-spin text-ui-fg-interactive" />
          ) : isError ? (
            <InvalidView error={error.message} />
          ) : (
            <AnimatePresence>
              {!success ? (
                <motion.div
                  key="create-account"
                  initial={false}
                  animate={{
                    height: "557px",
                    y: 0,
                  }}
                  exit={{
                    height: 0,
                    y: 40,
                  }}
                  transition={{
                    duration: 0.8,
                    delay: 0.6,
                    ease: [0, 0.71, 0.2, 1.01],
                  }}
                  className="w-full will-change-transform"
                >
                  <motion.div
                    initial={false}
                    animate={{
                      opacity: 1,
                      scale: 1,
                    }}
                    exit={{
                      opacity: 0,
                      scale: 0.7,
                    }}
                    transition={{
                      duration: 0.6,
                      delay: 0,
                      ease: [0, 0.71, 0.2, 1.01],
                    }}
                    key="inner-create-account"
                  >
                    <CreateView onSuccess={() => setSuccess(true)} token={token!} />
                  </motion.div>
                </motion.div>
              ) : (
                <motion.div
                  key="success-view"
                  initial={{
                    opacity: 0,
                    scale: 0.4,
                  }}
                  animate={{
                    opacity: 1,
                    scale: 1,
                  }}
                  transition={{
                    duration: 1,
                    delay: 0.6,
                    ease: [0, 0.71, 0.2, 1.01],
                  }}
                  className="w-full"
                >
                  <SuccessView />
                </motion.div>
              )}
            </AnimatePresence>
          )}
        </div>
      </div>
    </div>
  )
}

export const LoginLink = () => {
  const { t } = useTranslation()

  return (
    <div className="flex w-full flex-col items-center">
      <div className="my-6 h-px w-full border-b border-dotted" />
      <Link
        key="login-link"
        to="/login"
        className="hover:text-ui-fg-base-hover focus-visible:text-ui-fg-base-hover txt-small font-medium text-ui-fg-base outline-none transition-fg"
      >
        {t("invite.backToLogin")}
      </Link>
    </div>
  )
}

const InvalidView = ({ error }: { error?: string }) => {
  const { t } = useTranslation()

  return (
    <div className="flex flex-col items-center">
      <div className="flex flex-col items-center gap-y-1 text-center">
        <Heading>{error || t("invite.invalidTokenTitle")}</Heading>
        <Text size="small" className="text-ui-fg-subtle">
          {t("invite.invalidTokenHint")}
        </Text>
      </div>
      <LoginLink />
    </div>
  )
}

const CreateView = ({ onSuccess, token }: { onSuccess: () => void; token: string }) => {
  const { t } = useTranslation()
  const [invalid, setInvalid] = useState(false)

  const form = useForm<AcceptInvitationSchema>({
    resolver: zodResolver(acceptInvitationSchema),
    defaultValues: {
      name: "",
      password: "",
      repeatPassword: "",
    },
  })

  const { mutate: accept, isPending: isPendingAccept } = safQuery.useMutation(
    "post",
    "/api/admin/invitations/{token}/accept",
  )

  const handleSubmit = form.handleSubmit(async (data) => {
    accept(
      {
        params: {
          path: { token },
        },
        body: {
          name: data.name,
          password: data.password,
          confirmPassword: data.repeatPassword,
        },
      },
      {
        onSuccess() {
          toast.success(t("invite.toast.accepted"))

          onSuccess()
        },
        onError(error) {
          setInvalid(true)
          form.setError("root", {
            type: "manual",
            message: error.message,
          })
        },
      },
    )
  })

  const serverError = form.formState.errors.root?.message
  const validationError =
    form.formState.errors.password?.message ||
    form.formState.errors.repeatPassword?.message ||
    form.formState.errors.name?.message

  return (
    <div className="flex w-full flex-col items-center">
      <div className="mb-4 flex flex-col items-center">
        <Heading>{t("invite.title")}</Heading>
        <Text size="small" className="text-center text-ui-fg-subtle">
          {t("invite.hint")}
        </Text>
      </div>
      <Form {...form}>
        <form onSubmit={handleSubmit} className="flex w-full flex-col gap-y-6">
          <div className="flex flex-col gap-y-2">
            <Form.Field
              control={form.control}
              name="name"
              render={({ field }) => {
                return (
                  <Form.Item>
                    <Form.Control>
                      <Input
                        autoComplete="given-name"
                        {...field}
                        className="bg-ui-bg-field-component"
                        placeholder={t("fields.name")}
                      />
                    </Form.Control>
                  </Form.Item>
                )
              }}
            />
            <Form.Field
              control={form.control}
              name="password"
              render={({ field }) => {
                return (
                  <Form.Item>
                    <Form.Control>
                      <Input
                        autoComplete="new-password"
                        type="password"
                        {...field}
                        className="bg-ui-bg-field-component"
                        placeholder={t("fields.password")}
                      />
                    </Form.Control>
                  </Form.Item>
                )
              }}
            />
            <Form.Field
              control={form.control}
              name="repeatPassword"
              render={({ field }) => {
                return (
                  <Form.Item>
                    <Form.Control>
                      <Input
                        autoComplete="off"
                        type="password"
                        {...field}
                        className="bg-ui-bg-field-component"
                        placeholder={t("fields.repeatPassword")}
                      />
                    </Form.Control>
                  </Form.Item>
                )
              }}
            />
            {validationError && (
              <div className="mt-6 text-center">
                <Hint className="inline-flex" variant={"error"}>
                  {validationError}
                </Hint>
              </div>
            )}
            {serverError && (
              <Alert className="items-center bg-ui-bg-base p-2" dismissible variant="error">
                {serverError}
              </Alert>
            )}
          </div>
          <Button className="w-full" type="submit" isLoading={isPendingAccept} disabled={invalid}>
            {t("invite.createAccount")}
          </Button>
        </form>
      </Form>
      <LoginLink />
    </div>
  )
}

const SuccessView = () => {
  const { t } = useTranslation()

  return (
    <div className="flex w-full flex-col items-center gap-y-6">
      <div className="flex flex-col items-center gap-y-1 text-center">
        <Heading className="text-center">{t("invite.successTitle")}</Heading>
        <Text size="small" className="text-center text-ui-fg-subtle">
          {t("invite.successHint")}
        </Text>
      </div>
      <Button variant="secondary" asChild className="w-full">
        <Link to="/login" replace>
          {t("invite.successAction")}
        </Link>
      </Button>

      <Link
        key="login-link"
        to="/login"
        className="hover:text-ui-fg-base-hover focus-visible:text-ui-fg-base-hover txt-small font-medium text-ui-fg-base outline-none transition-fg"
      >
        {t("invite.backToLogin")}
      </Link>
    </div>
  )
}
