import { safQ<PERSON>y } from "@/client"
import AvatarBox from "@/components/common/logo-box/avatar-box"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON>, But<PERSON>, Heading, Hint, Input, Text, toast } from "@saf/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import { z } from "zod"
import { Form } from "../../components/common/form"
import { LoginLink, nameSchema, passwordSchema } from "../invite/invite"
import { i18n } from "@/i18n/i18n"

export const createAccountSchema = z
  .object({
    name: nameSchema,
    email: z.string().email("Please enter a valid email address").min(1, "Email is required"),
    password: passwordSchema,
    repeatPassword: z.string().min(1),
  })
  .superRefine(({ password, repeatPassword }, ctx) => {
    if (password !== repeatPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: i18n.t("invite.passwordMismatch"),
        path: ["repeatPassword"],
      })
    }
  })

export type CreateAccountSchema = z.infer<typeof createAccountSchema>

export const CreateAccount = () => {
  const { t } = useTranslation()

  const form = useForm<CreateAccountSchema>({
    resolver: zodResolver(createAccountSchema),
    defaultValues: {
      email: "",
      name: "",
      password: "",
      repeatPassword: "",
    },
  })

  const { mutate: register, isPending: isRegisterPending } = safQuery.useMutation(
    "post",
    "/api/admin/invitations/{token}/accept",
  )

  const handleSubmit = form.handleSubmit(async (data) => {
    await register(
      {
        params: {
          path: { token: "" },
        },
        body: {
          name: data.name,
          password: data.password,
          confirmPassword: data.repeatPassword,
        },
      },
      {
        onSuccess() {
          toast.success(t("invite.toast.accepted"))
        },
        onError(error) {
          form.setError("root", {
            type: "manual",
            message: error.message,
          })
        },
      },
    )
  })

  const serverError = form.formState.errors.root?.message
  const validationError =
    form.formState.errors.email?.message ||
    form.formState.errors.password?.message ||
    form.formState.errors.repeatPassword?.message ||
    form.formState.errors.name?.message

  return (
    <div className="flex min-h-dvh w-dvw items-center justify-center bg-ui-bg-subtle">
      <div className="m-4 flex w-full max-w-[280px] flex-col items-center">
        <AvatarBox />
        <div className="mb-4 flex flex-col items-center">
          <Heading>{t("invite.title")}</Heading>
          <Text size="small" className="text-center text-ui-fg-subtle">
            {t("invite.hint")}
          </Text>
        </div>
        <div className="flex w-full flex-col gap-y-3">
          <Form {...form}>
            <form onSubmit={handleSubmit} className="flex w-full flex-col gap-y-6">
              <div className="flex flex-col gap-y-2">
                <Form.Field
                  control={form.control}
                  name="email"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Control>
                          <Input
                            autoComplete="off"
                            {...field}
                            className="bg-ui-bg-field-component"
                            placeholder={t("fields.email")}
                          />
                        </Form.Control>
                      </Form.Item>
                    )
                  }}
                />
                <Form.Field
                  control={form.control}
                  name="name"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Control>
                          <Input
                            autoComplete="given-name"
                            {...field}
                            className="bg-ui-bg-field-component"
                            placeholder={t("fields.firstName")}
                          />
                        </Form.Control>
                      </Form.Item>
                    )
                  }}
                />
                <Form.Field
                  control={form.control}
                  name="password"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Control>
                          <Input
                            autoComplete="new-password"
                            type="password"
                            {...field}
                            className="bg-ui-bg-field-component"
                            placeholder={t("fields.password")}
                          />
                        </Form.Control>
                      </Form.Item>
                    )
                  }}
                />
                <Form.Field
                  control={form.control}
                  name="repeatPassword"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Control>
                          <Input
                            autoComplete="off"
                            type="password"
                            {...field}
                            className="bg-ui-bg-field-component"
                            placeholder={t("fields.repeatPassword")}
                          />
                        </Form.Control>
                      </Form.Item>
                    )
                  }}
                />
                {validationError && (
                  <div className="mt-6 text-center">
                    <Hint className="inline-flex" variant={"error"}>
                      {validationError}
                    </Hint>
                  </div>
                )}
                {serverError && (
                  <Alert className="items-center bg-ui-bg-base p-2" dismissible variant="error">
                    {serverError}
                  </Alert>
                )}
              </div>
              <Button className="w-full" type="submit" isLoading={isRegisterPending}>
                {t("invite.createAccount")}
              </Button>
            </form>
          </Form>
        </div>
        <LoginLink />
      </div>
    </div>
  )
}
