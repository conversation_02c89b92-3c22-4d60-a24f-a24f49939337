import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"
import { <PERSON><PERSON>, Button, Heading, Input, Text, toast } from "@saf/ui"
import { useForm } from "react-hook-form"
import { Trans, useTranslation } from "react-i18next"
import { Link, useNavigate, useSearchParams } from "react-router-dom"
import * as z from "zod"

import { useLogin } from "@/hooks/auth"
import { i18n } from "@/i18n/i18n"
import { useState } from "react"
import { decodeToken } from "react-jwt"
import { Form } from "../../components/common/form"
import { LogoBox } from "../../components/common/logo-box"
import { passwordSchema } from "../invite/invite"

const resetPasswordInstructionsSchema = z.object({
  email: z.string().email(),
})

const resetPasswordSchema = z
  .object({
    password: passwordSchema,
    repeatPassword: z.string().min(1),
  })
  .superRefine(({ password, repeatPassword }, ctx) => {
    if (password !== repeatPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: i18n.t("resetPassword.passwordMismatch"),
        path: ["repeat_password"],
      })
    }
  })
type ResetPasswordSchema = z.infer<typeof resetPasswordSchema>

const ResetPasswordTokenSchema = z.object({
  entity_id: z.string(),
  provider: z.string(),
  exp: z.number(),
  iat: z.number(),
})

type DecodedResetPasswordToken = {
  entity_id: string // -> email in here
  provider: string
  exp: string
  iat: string
}

const validateDecodedResetPasswordToken = (decoded: unknown): decoded is DecodedResetPasswordToken => {
  return ResetPasswordTokenSchema.safeParse(decoded).success
}

const InvalidResetToken = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()

  return (
    <div className="flex min-h-dvh w-dvw items-center justify-center bg-ui-bg-base">
      <div className="m-4 flex w-full max-w-[300px] flex-col items-center">
        <LogoBox className="mb-4" />
        <div className="mb-6 flex flex-col items-center">
          <Heading>{t("resetPassword.invalidLinkTitle")}</Heading>
          <Text size="small" className="text-center text-ui-fg-subtle">
            {t("resetPassword.invalidLinkHint")}
          </Text>
        </div>
        <div className="flex w-full flex-col gap-y-3">
          <Button onClick={() => navigate("/reset-password", { replace: true })} className="w-full" type="submit">
            {t("resetPassword.goToResetPassword")}
          </Button>
        </div>
        <span className="txt-small my-6">
          <Trans
            i18nKey="resetPassword.backToLogin"
            components={[
              <Link
                key="login-link"
                to="/login"
                className="text-ui-fg-interactive outline-none transition-fg hover:text-ui-fg-interactive-hover focus-visible:text-ui-fg-interactive-hover"
              />,
            ]}
          />
        </span>
      </div>
    </div>
  )
}

const ChooseNewPassword = ({ token }: { token: string }) => {
  const { t } = useTranslation()

  const [showAlert, setShowAlert] = useState(false)

  const invite: DecodedResetPasswordToken | null = token ? decodeToken(token) : null

  const isValidResetPasswordToken = invite && validateDecodedResetPasswordToken(invite)

  const form = useForm<ResetPasswordSchema>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      repeatPassword: "",
    },
  })

  // TODO: replace with correct API when ready
  const { mutateAsync, isPending } = useLogin()

  const handleSubmit = form.handleSubmit(async ({ password }) => {
    if (!invite) {
      return
    }

    await mutateAsync(
      {
        body: {
          // TODO: use the email from the token
          email: "",
          password,
        },
      },
      {
        onSuccess: () => {
          form.setValue("password", "")
          form.setValue("repeatPassword", "")
          setShowAlert(true)
        },
        onError: (error: any) => {
          toast.error(error.message)
        },
      },
    )
  })

  if (!isValidResetPasswordToken) {
    return <InvalidResetToken />
  }

  return (
    <div className="flex min-h-dvh w-dvw items-center justify-center bg-ui-bg-subtle">
      <div className="m-4 flex w-full max-w-[280px] flex-col items-center">
        <LogoBox className="mb-4" />
        <div className="mb-6 flex flex-col items-center">
          <Heading>{t("resetPassword.resetPassword")}</Heading>
          <Text size="small" className="text-center text-ui-fg-subtle">
            {t("resetPassword.newPasswordHint")}
          </Text>
        </div>
        <div className="flex w-full flex-col gap-y-3">
          <Form {...form}>
            <form onSubmit={handleSubmit} className="flex w-full flex-col gap-y-6">
              <div className="flex flex-col gap-y-4">
                <Input type="email" disabled value={invite?.entity_id} />
                <Form.Field
                  control={form.control}
                  name="password"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Control>
                          <Input
                            autoComplete="new-password"
                            type="password"
                            {...field}
                            placeholder={t("resetPassword.newPassword")}
                          />
                        </Form.Control>
                        <Form.ErrorMessage />
                      </Form.Item>
                    )
                  }}
                />
                <Form.Field
                  control={form.control}
                  name="repeatPassword"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Control>
                          <Input
                            autoComplete="off"
                            type="password"
                            {...field}
                            placeholder={t("resetPassword.repeatNewPassword")}
                          />
                        </Form.Control>
                        <Form.ErrorMessage />
                      </Form.Item>
                    )
                  }}
                />
              </div>
              {showAlert && (
                <Alert dismissible variant="success">
                  <div className="flex flex-col">
                    <span className="mb-1 text-ui-fg-base">{t("resetPassword.successfulResetTitle")}</span>
                    <span>{t("resetPassword.successfulReset")}</span>
                  </div>
                </Alert>
              )}
              {!showAlert && (
                <Button className="w-full" type="submit" isLoading={isPending}>
                  {t("resetPassword.resetPassword")}
                </Button>
              )}
            </form>
          </Form>
        </div>
        <span className="txt-small my-6">
          <Trans
            i18nKey="resetPassword.backToLogin"
            components={[
              <Link
                key="login-link"
                to="/login"
                className="hover:text-ui-fg-base-hover focus-visible:text-ui-fg-base-hover text-ui-fg-base outline-none transition-fg"
              />,
            ]}
          />
        </span>
      </div>
    </div>
  )
}

export const ResetPassword = () => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const [showAlert, setShowAlert] = useState(false)

  const token = searchParams.get("token")

  const form = useForm<z.infer<typeof resetPasswordInstructionsSchema>>({
    resolver: zodResolver(resetPasswordInstructionsSchema),
    defaultValues: {
      email: "",
    },
  })

  // TODO: replace with correct API when ready
  const { mutateAsync, isPending } = useLogin()

  const handleSubmit = form.handleSubmit(async ({ email }) => {
    await mutateAsync(
      {
        body: {
          email,
          // TODO: remove when Api is ready
          password: "",
        },
      },
      {
        onSuccess: () => {
          form.setValue("email", "")
          setShowAlert(true)
        },
        onError: (error: any) => {
          toast.error(error.message)
        },
      },
    )
  })

  if (token) {
    return <ChooseNewPassword token={token} />
  }

  return (
    <div className="flex min-h-dvh w-dvw items-center justify-center bg-ui-bg-base">
      <div className="m-4 flex w-full max-w-[300px] flex-col items-center">
        <LogoBox className="mb-4" />
        <div className="mb-4 flex flex-col items-center">
          <Heading>{t("resetPassword.resetPassword")}</Heading>
          <Text size="small" className="text-center text-ui-fg-subtle">
            {t("resetPassword.hint")}
          </Text>
        </div>
        <div className="flex w-full flex-col gap-y-3">
          <Form {...form}>
            <form onSubmit={handleSubmit} className="flex w-full flex-col gap-y-6">
              <div className="mt-4 flex flex-col gap-y-3">
                <Form.Field
                  control={form.control}
                  name="email"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Control>
                          <Input autoComplete="email" {...field} placeholder={t("fields.email")} />
                        </Form.Control>
                        <Form.ErrorMessage />
                      </Form.Item>
                    )
                  }}
                />
              </div>
              {showAlert && (
                <Alert dismissible variant="success">
                  <div className="flex flex-col">
                    <span className="mb-1 text-ui-fg-base">{t("resetPassword.successfulRequestTitle")}</span>
                    <span>{t("resetPassword.successfulRequest")}</span>
                  </div>
                </Alert>
              )}
              <Button className="w-full" type="submit" isLoading={isPending}>
                {t("resetPassword.sendResetInstructions")}
              </Button>
            </form>
          </Form>
        </div>
        <span className="txt-small my-6">
          <Trans
            i18nKey="resetPassword.backToLogin"
            components={[
              <Link
                key="login-link"
                to="/login"
                className="hover:text-ui-fg-base-hover focus-visible:text-ui-fg-base-hover text-ui-fg-base outline-none transition-fg"
              />,
            ]}
          />
        </span>
      </div>
    </div>
  )
}
