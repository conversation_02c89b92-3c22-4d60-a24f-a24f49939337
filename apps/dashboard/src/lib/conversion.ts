import { z } from "zod"

export function createEnumFromOptions<
  T extends { value: string },
  R extends string = T extends { value: infer R } ? R : never,
>(options: readonly T[]): [R, ...R[]] {
  return options.map((option) => option.value) as [R, ...R[]]
}

/** Converts a plain object's keys into readonly enum with type safety and autocompletion */
export function getEnumFromObjectKeys<
  TI extends Record<string, any>,
  R extends string = TI extends Record<infer R, any> ? R : never,
>(input: TI): [R, ...R[]] {
  return Object.keys(input) as [R, ...R[]]
}

/** Converts a plain object's keys into readonly enum with type safety and autocompletion */
export function getZodEnumFromObjectKeys<
  TI extends Record<string, any>,
  R extends string = TI extends Record<infer R, any> ? R : never,
>(input: TI): z.ZodEnum<[R, ...R[]]> {
  return z.enum(getEnumFromObjectKeys<TI, R>(input))
}
