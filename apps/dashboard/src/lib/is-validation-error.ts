import { components } from "@saf/sdk"

type ValidationError = components["schemas"]["ValidationError"]

export const isValidationError = (error: any): error is ValidationError => {
  return isRecord(error) && isRecord(error?.errors)
}

function isRecord(object: unknown): object is Record<keyof never, unknown> {
  return object instanceof Object && object.constructor === Object
}

type GenericError = components["schemas"]["GenericError"]

export const isGenericError = (error: any): error is GenericError => {
  return isRecord(error) && "message" in error && typeof error.message === "string"
}

export const showHumanFriendlyError = (error: any): string => {
  if (isValidationError(error)) {
    return Object.values(error.errors as Record<string, string[]>)
      .flatMap((fieldErrors) => (Array.isArray(fieldErrors) ? fieldErrors : [fieldErrors]))
      .join("\n")
  }

  if (isGenericError(error)) {
    return error.message
  }
  return "An unknown error occurred"
}
