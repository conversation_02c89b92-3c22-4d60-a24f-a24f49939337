import { PropsWithChildren } from "react"
import { ExtensionContext } from "./extension-context"

export const ExtensionProvider = ({ children }: PropsWithChildren) => {
  const getWidgets = (_location: string) => {
    // This is a simple implementation that returns no widgets
    // In a real application, this would fetch widgets from a registry
    return []
  }

  return <ExtensionContext.Provider value={{ getWidgets }}>{children}</ExtensionContext.Provider>
}
