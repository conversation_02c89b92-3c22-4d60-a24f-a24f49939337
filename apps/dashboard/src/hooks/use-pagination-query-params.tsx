import { useQueryParams } from "./use-query-params"

type PaginationQueryParams = "limit" | "sort" | "page" | string

type QueryParams<T extends PaginationQueryParams> = {
  [key in T]: string | undefined
} & {
  limit: string | undefined
  sort: string | undefined
  page: string | undefined
}

export function usePaginationQueryParam<T extends PaginationQueryParams>(keys: T[], prefix?: string): QueryParams<T> {
  const params = useQueryParams(["limit", "sort", "page", ...keys], prefix)
  const limit = params.limit ? parseInt(params.limit) : 16
  const page = params.page ? parseInt(params.page) : 1

  return {
    ...params,
    limit,
    page,
  }
}
