import { useDebounce } from "use-debounce"
import { useState } from "react"

/**
 * Hook for debouncing search input
 * @returns searchValue, onSearchValueChange, query
 */
export const useDebouncedSearch = () => {
  const [searchValue, onSearchValueChange] = useState("")
  const [debouncedQuery] = useDebounce(searchValue, 300)

  return {
    searchValue,
    onSearchValueChange,
    query: debouncedQuery || undefined,
  }
}
