import { safQuery } from "@/client"
import { tokenService } from "@/client/token-service"
import { TEAM_ID_STORAGE_KEY } from "@/routes/teams/team-redirect/team-redirect"
import { useMutation, useQueryClient } from "@tanstack/react-query"

export const useLogin = () => {
  const queryClient = useQueryClient()

  return safQuery.useMutation("post", "/api/admin/auth/login", {
    onSuccess: async (data) => {
      if (data.accessToken) {
        tokenService.saveTokens({
          accessToken: data.accessToken,
          refreshToken: data.refreshToken,
        })
        const userQueryKey = safQuery.queryOptions("get", "/api/admin/auth/me").queryKey

        const lastTeamId = localStorage.getItem(TEAM_ID_STORAGE_KEY)

        if (lastTeamId == null && data.user.teams && data.user.teams.length > 0) {
          localStorage.setItem(TEAM_ID_STORAGE_KEY, data.user.teams[0].id.toString())
        }

        queryClient.setQueryData(userQueryKey, data.user)
      }
    },
  })
}

export const useLogout = () => {
  const queryClient = useQueryClient()

  return useMutation<void, Error, void>({
    mutationFn: async () => {
      tokenService.clearTokens()
      queryClient.clear()
    },
  })
}

export const useSession = () => {
  return safQuery.useQuery(
    "get",
    "/api/admin/auth/me",
    {},
    {
      staleTime: Infinity,
      gcTime: Infinity,
      retry: false,
    },
  )
}
