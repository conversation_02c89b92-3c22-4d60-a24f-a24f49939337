#!/bin/bash

# Simple React S3 Deployment Script
# Usage: ./deploy.sh <bucket-name>

set -e # Exit on any error

# Check if environment is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <environment> [aws-profile]"
    echo "Available environments: demo, production"
    echo "Example: $0 demo my-aws-profile"
    exit 1
fi

ENVIRONMENT=$1
AWS_PROFILE=${2:-"innoqb"}

# Set bucket name based on environment
case $ENVIRONMENT in
"demo")
    BUCKET_NAME="saf-admin-demo"
    ;;
"production")
    BUCKET_NAME="saf-admin-production"
    ;;
*)
    echo "❌ Unknown environment: $ENVIRONMENT"
    echo "Available environments: demo, production"
    exit 1
    ;;
esac
BUILD_DIR="dist"

echo "🚀 Starting deployment to S3 bucket: $BUCKET_NAME (using profile: $AWS_PROFILE)"

# Check if required commands exist
if ! command -v aws &>/dev/null; then
    echo "❌ AWS CLI is not installed"
    exit 1
fi

if ! command -v pnpm &>/dev/null; then
    echo "❌ pnpm is not installed"
    exit 1
fi

# Check if we're in a React project directory
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Are you in the React app directory?"
    exit 1
fi

# Build the React application
echo "📦 Building React application..."
pnpm run build

# Check if build directory exists
if [ ! -d "$BUILD_DIR" ]; then
    echo "❌ Build directory not found after build"
    exit 1
fi

# Sync files to S3
echo "☁️  Uploading files to S3..."
aws s3 sync "$BUILD_DIR/" "s3://$BUCKET_NAME/" --delete --exclude "*.html" --profile "$AWS_PROFILE"

# Upload HTML files with no-cache headers
echo "📄 Uploading HTML files with no-cache headers..."
aws s3 sync "$BUILD_DIR/" "s3://$BUCKET_NAME/" --include "*.html" --cache-control "no-cache,no-store,must-revalidate" --profile "$AWS_PROFILE"

echo "✅ Deployment completed successfully!"
echo "📍 Files uploaded to: s3://$BUCKET_NAME/"
echo "Access your application at: https://$BUCKET_NAME.s3.ap-southeast-1.amazonaws.com/index.html"
