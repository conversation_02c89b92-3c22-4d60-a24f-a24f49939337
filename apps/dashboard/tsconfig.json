{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@ui/blocks/*": ["../../packages/ui/src/blocks/*"],
      "@ui/components/*": ["../../packages/ui/src/components/*"],
      "@ui/providers/*": ["../../packages/ui/src/providers/*"],
      "@ui/hooks/*": ["../../packages/ui/src/hooks/*"],
      "@ui/utils/*": ["../../packages/ui/src/utils/*"],
      "@ui/types/*": ["../../packages/ui/src/types/*"]
    },
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
