/// <reference types="vitest" />
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@ui/blocks": "./src/blocks",
      "@ui/components": "./src/components",
      "@ui/providers": "./src/providers",
      "@ui/hooks": "./src/hooks",
      "@ui/utils": "./src/utils",
      "@ui/types": "./src/types",
    },
  },
});
