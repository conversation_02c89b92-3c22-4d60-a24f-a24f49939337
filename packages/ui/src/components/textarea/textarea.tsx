import * as React from "react";

import { inputBaseStyles } from "@ui/components/input";
import { clx } from "@ui/utils/clx";

/**
 * This component is based on the `textarea` element and supports all of its props
 */
const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentPropsWithoutRef<"textarea"> & {
    variant?: "base" | "pop";
  }
>(({ className, variant = "base", ...props }, ref) => {
  return (
    <textarea
      ref={ref}
      className={clx(
        inputBaseStyles,
        "txt-small min-h-[60px] w-full px-2 py-1.5",
        variant == "base" && "bg-ui-bg-field hover:bg-ui-bg-field-hover",
        variant == "pop" &&
          "bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover",
        className,
      )}
      {...props}
    />
  );
});
Textarea.displayName = "Textarea";

export { Textarea };
