import * as React from "react";

import { useDataTableContext } from "@ui/blocks/data-table/context/use-data-table-context";
import { DropdownMenu } from "@ui/components/dropdown-menu";
import { IconButton } from "@ui/components/icon-button";
import { Skeleton } from "@ui/components/skeleton";
import { Tooltip } from "@ui/components/tooltip";
import { Funnel } from "@medusajs/icons";

interface DataTableFilterMenuProps {
  /**
   * The tooltip to show when hovering over the filter menu.
   */
  tooltip?: string;
}

/**
 * This component adds a filter menu to the data table, allowing users
 * to filter the table's data.
 */
const DataTableFilterMenu = (props: DataTableFilterMenuProps) => {
  const { instance } = useDataTableContext();

  const enabledFilters = Object.keys(instance.getFiltering());

  const filterOptions = instance
    .getFilters()
    .filter((filter) => !enabledFilters.includes(filter.id));

  if (!enabledFilters.length && !filterOptions.length) {
    throw new Error(
      "DataTable.FilterMenu was rendered but there are no filters to apply. Make sure to pass filters to 'useDataTable'",
    );
  }

  const Wrapper = props.tooltip ? Tooltip : React.Fragment;
  const wrapperProps = props.tooltip
    ? { content: props.tooltip, hidden: filterOptions.length === 0 }
    : ({} as any);

  if (instance.showSkeleton) {
    return <DataTableFilterMenuSkeleton />;
  }

  return (
    <DropdownMenu>
      <Wrapper {...wrapperProps}>
        <DropdownMenu.Trigger asChild disabled={filterOptions.length === 0}>
          <IconButton variant="secondary" size="small">
            <Funnel />
          </IconButton>
        </DropdownMenu.Trigger>
      </Wrapper>
      <DropdownMenu.Content side="bottom">
        {filterOptions.map((filter) => (
          <DropdownMenu.Item
            key={filter.id}
            onClick={() => {
              instance.addFilter({ id: filter.id, value: undefined });
            }}
          >
            {filter.label}
          </DropdownMenu.Item>
        ))}
      </DropdownMenu.Content>
    </DropdownMenu>
  );
};
DataTableFilterMenu.displayName = "DataTable.FilterMenu";

const DataTableFilterMenuSkeleton = () => {
  return <Skeleton className="size-7" />;
};

export { DataTableFilterMenu };
export type { DataTableFilterMenuProps };
