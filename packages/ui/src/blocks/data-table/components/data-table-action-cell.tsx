"use client";

import * as React from "react";

import { EllipsisHorizontal } from "@medusajs/icons";
import { CellContext } from "@tanstack/react-table";
import type { DataTableActionColumnDefMeta } from "@ui/blocks/data-table/types";
import { Button } from "@ui/components/button";
import { DropdownMenu } from "@ui/components/dropdown-menu";
import { IconButton } from "@ui/components/icon-button";

interface DataTableActionCellProps<TData> {
  ctx: CellContext<TData, unknown>;
}

const DataTableActionCell = <TData,>({
  ctx,
}: DataTableActionCellProps<TData>) => {
  const meta = ctx.column.columnDef.meta as
    | DataTableActionColumnDefMeta<TData>
    | undefined;
  const actions = meta?.___actions;

  if (!actions) {
    return null;
  }

  const resolvedActions =
    typeof actions === "function" ? actions(ctx) : actions;

  if (!Array.isArray(resolvedActions)) {
    return null;
  }

  if (meta?.___showAsButtons ?? true) {
    return (
      <div className="flex gap-2 shrink-0 ms-1">
        {resolvedActions.map((actionOrGroup, idx) => {
          const isArray = Array.isArray(actionOrGroup);

          return isArray ? (
            <React.Fragment key={idx}>
              {actionOrGroup.map((action) => {
                const Wrapper = action.label ? Button : IconButton;

                return (
                  <Wrapper
                    variant="secondary"
                    size="small"
                    key={action.label}
                    onClick={(e) => {
                      e.stopPropagation();
                      action.onClick(ctx);
                    }}
                    className="[&>svg]:text-ui-fg-subtle flex items-center gap-2 text-xs"
                  >
                    {action.icon}
                    {action.label}
                  </Wrapper>
                );
              })}
            </React.Fragment>
          ) : (
            <Button
              variant="secondary"
              size="small"
              key={actionOrGroup.label}
              onClick={(e) => {
                e.stopPropagation();
                actionOrGroup.onClick(ctx);
              }}
              className="[&>svg]:text-ui-fg-subtle flex items-center gap-2 text-xs"
            >
              {actionOrGroup.icon}
              {actionOrGroup.label}
            </Button>
          );
        })}
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenu.Trigger asChild className="ml-1">
        <IconButton size="small" variant="transparent">
          <EllipsisHorizontal />
        </IconButton>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content side="bottom">
        {resolvedActions.map((actionOrGroup, idx) => {
          const isArray = Array.isArray(actionOrGroup);
          const isLast = idx === resolvedActions.length - 1;

          return isArray ? (
            <React.Fragment key={idx}>
              {actionOrGroup.map((action) => (
                <DropdownMenu.Item
                  key={action.label}
                  onClick={(e) => {
                    e.stopPropagation();
                    action.onClick(ctx);
                  }}
                  className="[&>svg]:text-ui-fg-subtle flex items-center gap-2"
                >
                  {action.icon}
                  {action.label}
                </DropdownMenu.Item>
              ))}
              {!isLast && <DropdownMenu.Separator />}
            </React.Fragment>
          ) : (
            <DropdownMenu.Item
              key={actionOrGroup.label}
              onClick={(e) => {
                e.stopPropagation();
                actionOrGroup.onClick(ctx);
              }}
              className="[&>svg]:text-ui-fg-subtle flex items-center gap-2"
            >
              {actionOrGroup.icon}
              {actionOrGroup.label}
            </DropdownMenu.Item>
          );
        })}
      </DropdownMenu.Content>
    </DropdownMenu>
  );
};
DataTableActionCell.displayName = "DataTable.ActionCell";

export { DataTableActionCell };
export type { DataTableActionCellProps };
