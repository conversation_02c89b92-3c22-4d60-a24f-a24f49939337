{"name": "@saf/ui", "version": "4.0.9", "private": true, "scripts": {"lint": "eslint --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "devDependencies": {"@faker-js/faker": "^9.2.0", "@medusajs/ui-preset": "2.7.1", "@storybook/addon-essentials": "^8.3.5", "@storybook/addon-interactions": "^8.3.5", "@storybook/addon-links": "^8.3.5", "@storybook/addon-styling": "^1.3.7", "@storybook/blocks": "^8.3.5", "@storybook/react": "^8.3.5", "@storybook/react-vite": "^8.3.5", "@storybook/testing-library": "^0.2.2", "@testing-library/dom": "^9.3.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jsdom": "^21.1.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.1", "@vitest/coverage-v8": "^0.32.2", "autoprefixer": "^10.4.19", "chromatic": "^6.20.0", "eslint": "^7.32.0", "eslint-plugin-storybook": "^0.6.12", "jsdom": "^22.1.0", "postcss": "^8.4.38", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "resize-observer-polyfill": "^1.5.1", "rimraf": "^5.0.1", "storybook": "^8.3.5", "tailwindcss": "^3.4.3", "tsc-alias": "^1.8.7", "typescript": "^5.1.6", "vite": "^5.4.14", "vite-plugin-turbosnap": "^1.0.2", "vitest": "^3.0.5"}, "dependencies": {"@internationalized/date": "^3.8.0", "@medusajs/icons": "2.7.1", "@tanstack/react-table": "8.20.5", "clsx": "^1.2.1", "copy-to-clipboard": "^3.3.3", "cva": "1.0.0-beta.1", "prism-react-renderer": "^2.0.6", "prismjs": "^1.29.0", "radix-ui": "1.1.2", "react-aria": "^3.33.1", "react-currency-input-field": "^3.6.11", "react-stately": "^3.31.1", "sonner": "^1.5.0", "tailwind-merge": "^2.2.1"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "browserslist": ["last 3 chrome versions", "last 3 firefox versions", "last 3 opera versions", "last 3 edge versions", "last 3 safari versions", "last 3 chromeandroid versions", "last 1 firefoxandroid versions", "ios >= 13.4"], "exports": {".": "./src/index.ts", "./main.css": "./src/main.css", "./postcss.config": "./postcss.config.js", "./tailwind.config": "./tailwind.config.js"}}