// import createFetchClient from "openapi-fetch";
// import createQueryClient from "openapi-react-query";
// import { paths } from "../../openapi-ts-output/schema";

// export const createClient = ({ baseUrl }: { baseUrl: string }) => {
//   const fetchClient = createFetchClient<paths>({
//     baseUrl,
//   });

//   return {
//     fetchClient,
//     queryClient: createQueryClient(fetchClient),
//   };
// };

// export { createFetchClient, createQueryClient };
