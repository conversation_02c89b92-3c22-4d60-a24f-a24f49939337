// Core Types
export enum ActionType {
  navigateToPage = "navigate_to_page",
  openWebUrl = "open_web_url",
  callFunction = "call_function",
}

export type Action = {
  type: ActionType;
  value: string;
};

// Common Types
type Size = "small" | "medium" | "large";
type Alignment = "left" | "center" | "right";
type AspectRatio = "square" | "4:3" | "16:9" | "3:1";
type AlertType = "snackbar" | "toast" | "dialog";
type ChoiceStyle = "dropdown" | "radio";
type DateTimeFormat = "date" | "time" | "datetime";
type DateTimeRange = "all" | "earlier" | "later";
type ImageSource = "cameraAndPhoto" | "camera";

// Button Related Types
type ButtonStyle = "primary" | "secondary" | "tile";
type ButtonWidth = "auto" | "fill";
type ButtonAccent = "left" | "none" | "right";

// Base Interfaces
interface BaseWidget {
  name: string;
  type: string;
  action?: Action;
}

interface BaseData {
  data: Record<string, unknown>;
}

interface BaseDesign {
  design: Record<string, unknown>;
}

interface BaseOption {
  option: {
    visible?: boolean;
  };
}

interface BaseInput extends BaseData {
  data: {
    columnMap: string;
  };
  design: InputDesign;
}

// Design Types
interface InputDesign {
  label: string;
  placeholder: string;
}

interface InputOption {
  autofocus: true;
}

type DivOption = BaseOption & {
  option: {
    size: Size;
    visible: boolean;
  };
};

// Widget Types
export type Title = BaseWidget & {
  design: {
    style: "simple" | "image";
    size: "default" | "compact";
    imageFill: "fill" | "cover";
  };
  data: {
    title: string;
    emphasis: string;
    subtitle: string;
    image: string;
  };
};

export type Separator = DivOption;
export type Divider = DivOption;

export type Text = BaseOption & {
  data: {
    content: string;
  };
  design: {
    alignment: Alignment;
  };
  option: {
    isHeadline: boolean;
  };
};

export type RichText = BaseData & {
  data: {
    content: string;
  };
};

export type Alert = {
  data: {
    message: string;
  };
  option: {
    overlay: boolean;
    type: AlertType;
  };
};

export type RowTile = BaseData & {
  data: {
    leftContent: string;
    rightContent: string;
  };
};

export type Image = BaseData & {
  data: {
    imageData: string;
    altText: string;
  };
  option: {
    expandOnClick: boolean;
  };
};

export type Video = BaseData &
  BaseDesign & {
    data: string;
    design: {
      ratio: AspectRatio;
    };
  };

export type BigNumber = BaseWidget &
  BaseData & {
    data: {
      title: string;
      items: Array<{
        name: string;
        value: string;
        description: string;
        action?: Action;
      }>;
    };
  };

export type Button = {
  style: ButtonStyle;
  option: {
    width: ButtonWidth;
    accent?: ButtonAccent;
    isBlock: boolean;
    labelVisibility: boolean;
    showWhenDisabled: boolean;
  };
};

export type Link = {
  url: string;
};

// Input Types
export type TextEntry = BaseInput & {
  data: {
    columnMap: string;
    minLength: number;
    maxLength: number;
  };
  design: InputDesign;
  option: InputOption;
};

export type DateTime = BaseInput & {
  option: {
    range: DateTimeRange;
    format: DateTimeFormat;
  };
};

export type NumberEntry = BaseInput & {
  data: {
    columnMap: string;
    minValue: number;
    maxValue: number;
  };
  design: InputDesign;
  option: InputOption;
};

export type PhoneEmailEntry = BaseInput & {
  type: "email" | "phone";
};

export type Checkbox = BaseInput;

export type ImagePicker = BaseInput & {
  uploadFrom: ImageSource;
};

export type FilePicker = BaseInput;

export type Choice = BaseInput & {
  content: {
    source: string;
    value: string;
    image: string;
    displayAs: string;
  };
  design: {
    label: string;
    style: ChoiceStyle;
  };
  options: {
    itemLengthLimit: number;
    required: boolean;
    multiple: boolean;
  };
};
