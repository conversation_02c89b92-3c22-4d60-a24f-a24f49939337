import "@typespec/http";

using Http;

alias ValidationErrorResponse = {
  @statusCode statusCode: 400;
  @body error: ValidationError;
};

alias UnauthorizedErrorResponse = {
  @statusCode statusCode: 401;
  @body error: GenericError;
};

alias ForbiddenErrorResponse = {
  @statusCode statusCode: 403;
  @body error: GenericError;
};

alias NotFoundErrorResponse = {
  @statusCode statusCode: 404;
  @body error: GenericError;
};

alias InternalServerErrorResponse = {
  @statusCode statusCode: 500;
  @body error: GenericError;
};

alias AllErrorResponse = ValidationErrorResponse | NotFoundErrorResponse | InternalServerErrorResponse;
