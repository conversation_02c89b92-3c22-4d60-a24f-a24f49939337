import "@typespec/http";

using Http;

@tag("Admin - Auth")
@route("/auth")
namespace SAF.Admin.Auth;

@route("/login")
@post
op login(@body request: UserLoginRequest): {
  @statusCode statusCode: 200;
  @body response: UserLoginResponse;
} | AllErrorResponse;

@route("/refresh-token")
@post
op refreshToken(@body request: RefreshTokenRequest): {
  @statusCode statusCode: 200;
  @body response: RefreshTokenResponse;
} | AllErrorResponse;

@route("/me")
@get
op me(): {
  @statusCode statusCode: 200;
  @body response: UserResponse;
} | AllErrorResponse;
