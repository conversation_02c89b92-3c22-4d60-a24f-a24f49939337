import "@typespec/http";

using Http;

@tag("Admin - Users")
@route("/users")
namespace SAF.Admin.User;

@get
op listUsers(...PaginationParams): {
  @statusCode status: 200;
  @body response: PagedResult<UserResponse>;
} | AllErrorResponse;

@get
op getUser(@path userId: int32): {
  @statusCode statusCode: 200;
  @body user: UserResponse;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op updateUser(@path userId: int32, @body user: UpdateUserRequest): {
  @statusCode statusCode: 200;
  @body updatedUser: UserResponse;
} | AllErrorResponse;

@delete
op deleteUser(@path userId: int32): {
  @statusCode statusCode: 204;
} | AllErrorResponse;
