import "@typespec/http";

using Http;

@tag("Admin - Data Source")
@route("/{dataSourceId}/external-apis")
namespace SAF.Admin.Team.DataSource.ExternalApi;

@post
op create(
  @path teamId: int32,
  @path miniAppId: int32,
  @path dataSourceId: int32,
  @body externalApi: CreateExternalApiRequest,
): {
  @statusCode statusCode: 201;
  @body newExternalApi: ExternalApiResponse;
} | AllErrorResponse;

@get
op list(
  @path teamId: int32,
  @path miniAppId: int32,
  @path dataSourceId: int32,
  ...PaginationParams,
): {
  @statusCode statusCode: 200;
  @body response: PagedResult<ExternalApiResponse>;
} | AllErrorResponse;

@get
op get(
  @path teamId: int32,
  @path miniAppId: int32,
  @path dataSourceId: int32,
  @path externalApiId: int32,
): {
  @statusCode statusCode: 200;
  @body externalApi: ExternalApiResponse;
} | AllErrorResponse;

@route("/{externalApiId}/execute")
@get
op execute(
  @path teamId: int32,
  @path miniAppId: int32,
  @path dataSourceId: int32,
  @path externalApiId: int32,
): {
  @statusCode statusCode: 200;
  @body externalApi: ExternalApiResponseSnapshot;
} | AllErrorResponse;

@route("/{externalApiId}/external-api-snapshots")
@get
op getSnapshots(
  @path teamId: int32,
  @path miniAppId: int32,
  @path dataSourceId: int32,
  @path externalApiId: int32,
): {
  @statusCode statusCode: 200;
  @body externalApi: ExternalApiResponseSnapshot;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op update(
  @path teamId: int32,
  @path miniAppId: int32,
  @path dataSourceId: int32,
  @path externalApiId: int32,
  @body externalApi: UpdateExternalApiRequest,
): {
  @statusCode statusCode: 200;
  @body updatedexternalApi: ExternalApiResponse;
} | AllErrorResponse;

@delete
op delete(
  @path teamId: int32,
  @path miniAppId: int32,
  @path dataSourceId: int32,
  @path externalApiId: int32,
): {
  @statusCode statusCode: 204;
} | AllErrorResponse;
