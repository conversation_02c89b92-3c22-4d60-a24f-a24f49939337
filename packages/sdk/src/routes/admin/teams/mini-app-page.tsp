import "@typespec/http";

using Http;

@tag("Admin - Mini App Page")
@route("/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages")
namespace SAF.Admin.Team.MiniAppPage;

@post
op create(
  @path teamId: int32,
  @path miniAppId: int32,
  @path versionId: int32,
  @body page: CreateMiniAppPageRequest,
): {
  @statusCode statusCode: 201;
  @body newPage: MiniAppPageResponse;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op update(
  @path teamId: int32,
  @path miniAppId: int32,
  @path versionId: int32,
  @path pageId: int32,
  @body page: UpdateMiniAppPageRequest,
): {
  @statusCode statusCode: 200;
  @body updatedPage: MiniAppPageResponse;
} | AllErrorResponse;

@route("/{pageId}/move")
@patch(#{ implicitOptionality: true })
op move(
  @path teamId: int32,
  @path miniAppId: int32,
  @path versionId: int32,
  @path pageId: int32,
  @body request: MoveRequest,
): {
  @statusCode statusCode: 200;
} | AllErrorResponse;

@delete
op delete(
  @path teamId: int32,
  @path miniAppId: int32,
  @path versionId: int32,
  @path pageId: int32,
): {
  @statusCode statusCode: 204;
} | AllErrorResponse;
