import "@typespec/http";

using Http;

@tag("Admin - Mini app")
@route("/mini-apps")
namespace SAF.Admin.Team.MiniApp;

@post
op createMiniApp(@path teamId: int32, @body miniApp: CreateMiniAppRequest): {
  @statusCode statusCode: 201;
  @body newMiniApp: MiniAppResponse;
} | AllErrorResponse;

@get
op listMiniApps(@path teamId: int32, ...PaginationParams): {
  @statusCode statusCode: 200;
  @body response: PagedResult<MiniAppResponse>;
} | AllErrorResponse;

@get
op getMiniApp(@path teamId: int32, @path miniAppId: int32): {
  @statusCode statusCode: 200;
  @body miniApp: MiniAppResponse;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op updateMiniApp(
  @path teamId: int32,
  @path miniAppId: int32,
  @body miniApp: UpdateMiniAppRequest,
): {
  @statusCode statusCode: 200;
  @body updatedMiniApp: MiniAppResponse;
} | AllErrorResponse;

@delete
op deleteMiniApp(@path teamId: int32, @path miniAppId: int32): {
  @statusCode statusCode: 204;
} | AllErrorResponse;
