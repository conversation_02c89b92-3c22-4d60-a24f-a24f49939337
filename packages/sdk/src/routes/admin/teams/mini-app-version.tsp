import "@typespec/http";

using Http;

@tag("Admin - Mini App Version")
@route("/mini-apps/{miniAppId}/mini-app-versions")
namespace SAF.Admin.Team.MiniAppVersion;

@post
op create(
  @path teamId: int32,
  @path miniAppId: int32,
  @body version: CreateMiniAppVersionRequest,
): {
  @statusCode statusCode: 201;
  @body newVersion: MiniAppVersionDetail;
} | AllErrorResponse;

@get
op list(@path teamId: int32, @path miniAppId: int32, ...PaginationParams): {
  @statusCode statusCode: 200;
  @body response: PagedResult<MiniAppVersionListItem>;
} | AllErrorResponse;

@get
op get(@path teamId: int32, @path miniAppId: int32, @path versionId: int32): {
  @statusCode statusCode: 200;
  @body version: MiniAppVersionDetail;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op update(
  @path teamId: int32,
  @path miniAppId: int32,
  @path versionId: int32,
  @body version: UpdateMiniAppVersionRequest,
): {
  @statusCode statusCode: 200;
  @body updatedVersion: MiniAppVersionDetail;
} | AllErrorResponse;

@delete
op delete(
  @path teamId: int32,
  @path miniAppId: int32,
  @path versionId: int32,
): {
  @statusCode statusCode: 204;
} | AllErrorResponse;
