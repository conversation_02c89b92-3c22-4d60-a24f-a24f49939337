import "@typespec/http";

using Http;

@tag("Admin - Invitation")
@route("/invitations")
namespace SAF.Admin.Team.Invitation;

@post
op inviteToTeam(@path teamId: int32, @body request: InviteToTeamRequest): {
  @statusCode statusCode: 200;
} | AllErrorResponse;

@get
op listTeamInvitations(@path teamId: int32, ...PaginationParams): {
  @statusCode statusCode: 200;
  @body response: PagedResult<InvitationResponse>;
} | AllErrorResponse;

@route("/{invitationId}/cancel")
@delete
op cancelTeamInvitation(@path teamId: int32, @path invitationId: int32): {
  @statusCode statusCode: 200;
} | AllErrorResponse;
