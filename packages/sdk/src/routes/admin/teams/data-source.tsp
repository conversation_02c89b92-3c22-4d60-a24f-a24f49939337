import "@typespec/http";

using Http;

@tag("Admin - Data Source")
@route("/mini-apps/{miniAppId}/data-sources")
namespace SAF.Admin.Team.DataSource;

@post
op create(
  @path teamId: int32,
  @path miniAppId: int32,
  @body dataSource: CreateDataSourceRequest,
): {
  @statusCode statusCode: 201;
  @body newDataSource: DataSourceResponse;
} | AllErrorResponse;

@get
op list(@path teamId: int32, @path miniAppId: int32, ...PaginationParams): {
  @statusCode statusCode: 200;
  @body response: PagedResult<DataSourceResponse>;
} | AllErrorResponse;

@get
op get(
  @path teamId: int32,
  @path miniAppId: int32,
  @path dataSourceId: int32,
): {
  @statusCode statusCode: 200;
  @body dataSource: DataSourceResponse;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op update(
  @path teamId: int32,
  @path miniAppId: int32,
  @path dataSourceId: int32,
  @body dataSource: UpdateDataSourceRequest,
): {
  @statusCode statusCode: 200;
  @body updatedDataSource: DataSourceResponse;
} | AllErrorResponse;

@delete
op delete(
  @path teamId: int32,
  @path miniAppId: int32,
  @path dataSourceId: int32,
): {
  @statusCode statusCode: 204;
} | AllErrorResponse;
