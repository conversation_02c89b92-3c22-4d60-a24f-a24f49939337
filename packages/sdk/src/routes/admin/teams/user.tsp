import "@typespec/http";

using Http;

@tag("Admin - Users")
@route("/users")
namespace SAF.Admin.Team.User;

@get
op listTeamUsers(
  @path teamId: int32,
  @query email?: string,
  @query name?: string,
  ...PaginationParams,
): {
  @statusCode statusCode: 200;
  @body response: PagedResult<TeamUserResponse>;
} | AllErrorResponse;

@get
op getTeamUser(@path teamId: int32, @path userId: int32): {
  @statusCode statusCode: 200;
  @body user: TeamUserResponse;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op updateTeamUser(
  @path teamId: int32,
  @path userId: int32,
  @body user: TeamUserResponse,
): {
  @statusCode statusCode: 200;
  @body updatedUser: TeamUserResponse;
} | AllErrorResponse;

@delete
op deleteTeamUser(@path teamId: int32, @path userId: int32): {
  @statusCode statusCode: 204;
} | AllErrorResponse;
