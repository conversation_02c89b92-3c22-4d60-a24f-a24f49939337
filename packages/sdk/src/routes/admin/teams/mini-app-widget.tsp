import "@typespec/http";

using Http;

@tag("Admin - Mini App Widget")
@route("/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets")
namespace SAF.Admin.Team.MiniAppWidget;

@post
op create(
  @path teamId: int32,
  @path miniAppId: int32,
  @path versionId: int32,
  @path pageId: int32,
  @body widget: CreateMiniAppWidgetRequest,
): {
  @statusCode statusCode: 201;
  @body newWidget: MiniAppWidgetResponse;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op update(
  @path teamId: int32,
  @path miniAppId: int32,
  @path versionId: int32,
  @path pageId: int32,
  @path widgetId: int32,
  @body widget: UpdateMiniAppWidgetRequest,
): {
  @statusCode statusCode: 200;
  @body updatedWidget: MiniAppWidgetResponse;
} | AllErrorResponse;

@route("/{widgetId}/move")
@patch(#{ implicitOptionality: true })
op move(
  @path teamId: int32,
  @path miniAppId: int32,
  @path versionId: int32,
  @path pageId: int32,
  @path widgetId: int32,
  @body request: MoveRequest,
): {
  @statusCode statusCode: 200;
} | AllErrorResponse;

@delete
op delete(
  @path teamId: int32,
  @path miniAppId: int32,
  @path versionId: int32,
  @path pageId: int32,
  @path widgetId: int32,
): {
  @statusCode statusCode: 204;
} | AllErrorResponse;
