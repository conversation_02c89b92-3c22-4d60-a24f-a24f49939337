import "@typespec/http";

using Http;

@tag("Admin - Invitation")
@route("/invitations")
namespace SAF.Admin.Invitation;

@route("/{token}")
@get
op validateInvitation(@path token: string): {
  @statusCode statusCode: 200;
} | AllErrorResponse;

@route("/{token}/accept")
@post
op acceptInvitation(
  @path token: string,
  @body request: AcceptInvitationRequest,
): {
  @statusCode statusCode: 200;
} | AllErrorResponse;

@post
op inviteToSystem(@body request: InviteToSystemRequest): {
  @statusCode statusCode: 200;
} | AllErrorResponse;

@get
op listInvitations(...PaginationParams): {
  @statusCode statusCode: 200;
  @body response: PagedResult<InvitationResponse>;
} | AllErrorResponse;

@route("/{invitationId}/cancel")
@delete
op cancelInvitation(@path invitationId: int32): {
  @statusCode statusCode: 200;
} | AllErrorResponse;
