enum UserStatus {
  active: "active",
  inactive: "inactive",
  suspended: "suspended",
  deleted: "deleted",
}

model BaseUser {
  id: int64;
  email: string;
  name?: string;
  userStatus: UserStatus;
  createdAt: utcDateTime;
}

model TeamUserResponse extends BaseUser {
  teamRoles?: TeamRole[];
}

model UserResponse extends BaseUser {
  systemRoles?: SystemRole[];
  teams?: UserTeamRoles[];
  createdAt: utcDateTime;
}

model UserTeamRoles {
  id: int32;
  name: string;
  roles: TeamRole[];
}

model UpdateUserRequest is OmitProperties<UserResponse, "id">;
