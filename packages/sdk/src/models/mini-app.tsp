enum MiniAppStatus {
  removedByAdmin: "removed_by_admin",
  active: "active",
}

model MiniApp {
  id: int32;
  name: string;
  description?: string;
  customerServiceContactNumber?: string;
  customerServiceContactEmail?: string;
  termsAndConditionsUrl?: string;
  miniAppStatus: MiniAppStatus;
  categoryId?: int32;
  teamId: int32;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

model CreateMiniAppRequest
  is OmitProperties<
    MiniApp,

      | "id"
      | "createdAt"
      | "createdBy"
      | "updatedAt"
      | "updatedBy"
      | "teamId"
      | "miniAppStatus"
  >;

model UpdateMiniAppRequest
  is OmitProperties<
    MiniApp,
    "id" | "createdAt" | "createdBy" | "updatedAt" | "updatedBy" | "teamId"
  >;

alias MiniAppResponse = MiniApp;
alias MiniAppList = PagedResult<MiniApp>;
