enum DataSourceType {
  internalTable: "internal_table",
  externalApi: "external_api",
}

model BaseDataSource {
  id: int32;
  name: string;
  dataSourceType: DataSourceType;
  description?: string;
  createdAt: utcDateTime;
  createdBy: utcDateTime;
  updatedAt?: utcDateTime;
  updatedBy?: utcDateTime;
}

model InternalTableDataSource extends BaseDataSource {
  dataSourceType: DataSourceType.internalTable;
  config?: InternalTableConfig;
}

model ExternalApiDataSource extends BaseDataSource {
  dataSourceType: DataSourceType.externalApi;
  config?: ExternalApiConfig;
}

alias Meta = "id" | "createdAt" | "createdBy" | "updatedAt" | "updatedBy";

model CreateInternalTableDataSourceRequest
  is OmitProperties<InternalTableDataSource, Meta>;

model CreateExternalApiDataSourceRequest
  is OmitProperties<ExternalApiDataSource, Meta>;

model UpdateInternalTableDataSourceRequest
  is OmitProperties<InternalTableDataSource, Meta>;

model UpdateExternalApiDataSourceRequest
  is OmitProperties<ExternalApiDataSource, Meta>;

alias CreateDataSourceRequest = CreateExternalApiDataSourceRequest | CreateInternalTableDataSourceRequest;

alias UpdateDataSourceRequest = UpdateExternalApiDataSourceRequest | UpdateInternalTableDataSourceRequest;

alias DataSourceResponse = InternalTableDataSource | ExternalApiDataSource;

enum InternalTablePermissionType {
  allow: "allow",
  deny: "deny",
  owner: "owner",
}

model InternalTablePermissions {
  create: InternalTablePermissionType;
  read: InternalTablePermissionType;
  update: InternalTablePermissionType;
  delete: InternalTablePermissionType;
}

enum InternalTableFieldType {
  string: "string",
  number: "number",
  boolean: "boolean",
  dateTime: "datetime",
  image: "image",
}

model InternalTableSchema {
  name: string;
  type: InternalTableFieldType;
  required: boolean;
}

model InternalTableConfig {
  permissions: InternalTablePermissions;
  schema: InternalTableSchema[];
}

model ExternalApiRequestConfig {
  routeParameters?: Record<string>;
  headers?: Record<string>;
  queryParameters?: Record<string>;
  body?: Record<unknown>;
  cookies?: Record<string>;
}

model ExternalApiConfig extends ExternalApiRequestConfig {
  name: string;
  baseUrl?: string;
}
