model InviteToTeamRequest {
  email: string;
  roles: string[];
}

model InviteToSystemRequest {
  email: string;
  systemRoles: string[];
}

model AcceptInvitationRequest {
  name: string;
  password: string;
  confirmPassword?: string;
}

enum InvitationStatus {
  pending: "pending",
  accepted: "accepted",
  cancelled: "cancelled",
}

model InvitationResponse {
  id: int64;
  email: string;
  invitationStatus: InvitationStatus;
  systemRoles?: SystemRole[];
  teamAssignments?: {
    teamId: int32;
    teamName: string;
    teamRoles: TeamRole[];
  }[];
  token: string;
  invitedBy: {
    id: int32;
    name: string;
    email: string;
  };
  expiredAt: string;
  createdAt: string;
}
