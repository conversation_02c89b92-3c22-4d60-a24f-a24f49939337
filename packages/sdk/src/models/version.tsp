enum MiniAppType {
  url: "url",
  bundle: "bundle",
  builder: "builder",
}

enum MiniAppVersionStatus {
  draft: "draft",
  inReview: "in_review",
  approved: "approved",
  live: "live",
  rejected: "rejected",
}

model MiniAppVersion {
  id: int32;
  version: string;
  miniAppType: MiniAppType;
  miniAppVersionStatus: MiniAppVersionStatus;
  releaseNote?: string;
  miniAppUrl?: string;
  url?: string;
  thumbnailUrl?: string;
  s3Key?: string;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

model CreateMiniAppVersionRequest
  is OmitProperties<
    MiniAppVersion,

      | "id"
      | "miniAppVersionStatus"
      | "s3Key"
      | "createdAt"
      | "createdBy"
      | "updatedAt"
      | "updatedBy"
  >;

model UpdateMiniAppVersionRequest
  is OmitProperties<CreateMiniAppVersionRequest, "miniAppVersionStatus">;

model MiniAppVersionDetail extends MiniAppVersion {
  pages: MiniAppPage[];
}

alias MiniAppVersionListItem = MiniAppVersion;
