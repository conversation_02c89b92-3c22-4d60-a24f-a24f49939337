model UserLoginRequest {
  email: string;
  password: string;
}

model UserLoginResponse {
  user: UserResponse;
  refreshToken: string;
  accessToken: string;
}

model RefreshTokenRequest {
  userId: string;
  refreshToken: string;
}

model RefreshTokenResponse {
  refreshToken: string;
  accessToken: string;
}

enum SystemRole {
  admin: "system_admin",
  editor: "system_editor",
}

enum TeamRole {
  admin: "team_admin",
  editor: "team_developer",
}
