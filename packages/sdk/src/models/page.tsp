model MiniAppPage {
  id: int32;
  miniAppVersionId: int32;
  name: string;
  title?: string;
  icon?: string;
  isHidden: boolean;
  hideInNavbar: boolean;
  position: int32;
  widgets?: Widgets[];
}

model CreateMiniAppPageRequest
  is OmitProperties<
    MiniAppPage,
    "id" | "createdAt" | "createdBy" | "updatedAt" | "updatedBy"
  >;

model UpdateMiniAppPageRequest
  is OmitProperties<
    MiniAppPage,

      | "id"
      | "miniAppVersionId"
      | "createdAt"
      | "createdBy"
      | "updatedAt"
      | "updatedBy"
  >;

alias MiniAppPageResponse = MiniAppPage;
